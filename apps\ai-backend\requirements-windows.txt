# Windows-Compatible Real AI Backend Dependencies
# Install with: pip install -r requirements-windows.txt

# Core FastAPI Backend
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# Machine Learning & NLP (Windows Compatible)
torch==2.1.0
transformers==4.35.0
sentence-transformers==2.2.2
scikit-learn==1.3.0
numpy>=1.26.0
pandas==2.0.3

# Computer Vision (Windows Compatible)
opencv-python==********
Pillow==10.0.1
mediapipe==0.10.8

# GenConViT Dependencies
timm==0.9.12

# Audio Processing (Optional - may require additional setup)
# librosa==0.10.1
# soundfile==0.12.1

# Database & Caching
sqlalchemy==2.0.23
aiofiles==23.2.1

# Utilities
python-dotenv==1.0.0
httpx==0.25.0

# Monitoring & Logging
structlog==23.2.0

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0

# Additional Windows-compatible packages
requests==2.31.0
tqdm==4.66.1
