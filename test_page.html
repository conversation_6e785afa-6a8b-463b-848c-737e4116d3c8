<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentinel Extension Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .post {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .high-risk {
            border-left: 4px solid #ef4444;
        }
        .medium-risk {
            border-left: 4px solid #f59e0b;
        }
        .low-risk {
            border-left: 4px solid #10b981;
        }
        .video-container {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #6366f1;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🛡️ Sentinel Extension Demo Page</h1>
    <p>This page contains various types of content to test the Sentinel browser extension.</p>
    
    <div class="post high-risk">
        <h3>🚨 Critical Fraud Example</h3>
        <p>GUARANTEED 1000% PROFIT IN 24 HOURS! Secret Wall Street algorithm revealed! URGENT - only 50 spots left! Risk-free investment opportunity! 🚀💰</p>
    </div>
    
    <div class="post medium-risk">
        <h3>⚠️ Pump & Dump Example</h3>
        <p>🚀 MOON TIME! Diamond hands! This crypto is going to PUMP! Get in before it rockets to the moon! HODL! Apes together strong! 💎🙌</p>
    </div>
    
    <div class="post low-risk">
        <h3>✅ Legitimate Content</h3>
        <p>Market analysis suggests diversified portfolio allocation for long-term growth. Consider 60% equities, 30% bonds, 10% alternatives. Consult your financial advisor for personalized investment advice.</p>
    </div>
    
    <div class="post high-risk">
        <h3>🎭 Insider Trading Claims</h3>
        <p>EXCLUSIVE insider information leaked! Warren Buffett's secret strategy revealed! Download confidential report before it's removed! Limited time access!</p>
    </div>
    
    <div class="video-container">
        <h3>🎬 Video Deepfake Test</h3>
        <p>The extension should analyze this video for deepfake content:</p>
        <video controls width="400">
            <source src="https://deepfake-generator.com/sample-fake-video.mp4" type="video/mp4">
            <source src="https://synthetic-media.ai/test-deepfake.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <p><small>Note: These are test URLs - the extension will analyze the URL patterns for deepfake indicators.</small></p>
    </div>
    
    <div class="post medium-risk">
        <h3>🔥 Emotional Manipulation</h3>
        <p>You'll REGRET missing this opportunity FOREVER! FOMO is real! Last chance to join exclusive investment club! Act NOW or lose everything! Don't let others get rich while you stay poor!</p>
    </div>
    
    <h2>🧪 Extension Testing Instructions</h2>
    <ol>
        <li><strong>Install Extension:</strong> Load unpacked from <code>apps/browser-extension/</code></li>
        <li><strong>Reload Page:</strong> Refresh this page after installation</li>
        <li><strong>Look for Badges:</strong> Red/Yellow/Green Sentinel scores should appear next to posts</li>
        <li><strong>Test Video:</strong> Click play on the video above</li>
        <li><strong>Check Notifications:</strong> Watch for deepfake alerts</li>
        <li><strong>View Stats:</strong> Click the Sentinel extension icon in toolbar</li>
    </ol>
    
    <h2>🎯 Expected Results</h2>
    <ul>
        <li>🔴 <strong>Critical posts</strong> should show red badges with low scores (0-30)</li>
        <li>🟡 <strong>Medium risk posts</strong> should show yellow badges (30-70)</li>
        <li>🟢 <strong>Safe posts</strong> should show green badges (70-100)</li>
        <li>🎭 <strong>Video analysis</strong> should trigger deepfake warnings</li>
        <li>🛡️ <strong>Page warnings</strong> should appear for high-risk content</li>
    </ul>
    
    <div style="margin-top: 40px; padding: 20px; background: #f0f9ff; border-radius: 8px;">
        <h3>🏆 Demo Success Indicators</h3>
        <p>If the extension is working correctly, you should see:</p>
        <ul>
            <li>✅ Sentinel badges appearing next to each post above</li>
            <li>✅ Red badges on fraud content (scores 0-30)</li>
            <li>✅ Yellow badges on pump & dump content (scores 30-70)</li>
            <li>✅ Green badges on legitimate content (scores 70+)</li>
            <li>✅ Video analysis triggering when you play the video</li>
            <li>✅ Browser notifications for deepfake detection</li>
        </ul>
    </div>
    
    <script>
        // Add some dynamic content to test extension
        setTimeout(() => {
            const dynamicPost = document.createElement('div');
            dynamicPost.className = 'post high-risk';
            dynamicPost.innerHTML = `
                <h3>🚨 Dynamic Content Test</h3>
                <p>URGENT! Secret crypto pump happening NOW! 500% guaranteed returns! Don't miss out!</p>
            `;
            document.body.appendChild(dynamicPost);
            console.log('🧪 Added dynamic content for extension testing');
        }, 3000);
    </script>
</body>
</html>
