#!/usr/bin/env python3
"""
Test script for Sentinel Real AI Backend
Tests both fraud detection and deepfake detection capabilities
"""

import asyncio
import json
import logging
import requests
import time
from typing import Dict, List

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

API_BASE_URL = "http://127.0.0.1:8000"

def test_health_check():
    """Test if the AI backend is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            logger.info("✅ AI Backend is healthy!")
            logger.info(f"Models loaded: {health_data.get('models_loaded', {})}")
            return True
        else:
            logger.error(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Could not connect to AI backend: {e}")
        return False

def test_fraud_detection():
    """Test the real AI fraud detection"""
    logger.info("🔍 Testing AI Fraud Detection...")
    
    test_texts = [
        {
            "text": "GUARANTEED 1000% RETURNS! Secret trading algorithm revealed! Act now before it's too late!",
            "expected": "high_risk"
        },
        {
            "text": "Invest in our exclusive crypto opportunity. Limited time offer with insider information!",
            "expected": "high_risk"
        },
        {
            "text": "Check out this interesting article about market trends and economic analysis.",
            "expected": "low_risk"
        },
        {
            "text": "Just had a great coffee this morning. Hope everyone has a wonderful day!",
            "expected": "low_risk"
        },
        {
            "text": "URGENT: Double your Bitcoin in 24 hours with this secret method! Celebrity endorsed!",
            "expected": "critical"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_texts, 1):
        try:
            logger.info(f"Test {i}: Analyzing '{test_case['text'][:50]}...'")
            
            response = requests.post(
                f"{API_BASE_URL}/api/analyze-text",
                json={
                    "text": test_case["text"],
                    "url": "https://test.example.com",
                    "context": {"platform": "test", "test_case": i}
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                fraud_prob = result.get('fraud_probability', 0)
                confidence = result.get('confidence', 0)
                threat_level = result.get('threat_level', 'unknown')
                risk_indicators = result.get('risk_indicators', [])
                
                logger.info(f"  🤖 AI Result: {fraud_prob:.2%} fraud probability")
                logger.info(f"  📊 Confidence: {confidence:.2%}")
                logger.info(f"  ⚠️ Threat Level: {threat_level}")
                logger.info(f"  🔍 Risk Indicators: {', '.join(risk_indicators[:3])}")
                
                results.append({
                    "test_case": i,
                    "text": test_case["text"][:50] + "...",
                    "expected": test_case["expected"],
                    "fraud_probability": fraud_prob,
                    "confidence": confidence,
                    "threat_level": threat_level,
                    "risk_indicators": risk_indicators
                })
                
            else:
                logger.error(f"  ❌ API Error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"  ❌ Test {i} failed: {e}")
        
        # Small delay between requests
        time.sleep(1)
    
    return results

def test_deepfake_detection():
    """Test the GenConViT deepfake detection"""
    logger.info("🎭 Testing GenConViT Deepfake Detection...")
    
    test_videos = [
        {
            "video_url": "https://example.com/real_video.mp4",
            "expected": "low_risk"
        },
        {
            "video_url": "https://example.com/deepfake_video.mp4", 
            "expected": "high_risk"
        },
        {
            "video_url": "https://example.com/synthetic_face.mp4",
            "expected": "critical"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_videos, 1):
        try:
            logger.info(f"Video Test {i}: Analyzing '{test_case['video_url']}'")
            
            response = requests.post(
                f"{API_BASE_URL}/api/analyze-video",
                json={
                    "video_url": test_case["video_url"],
                    "page_url": "https://test.example.com",
                    "video_metadata": {"test_case": i, "duration": 30}
                },
                timeout=60  # Video analysis takes longer
            )
            
            if response.status_code == 200:
                result = response.json()
                deepfake_prob = result.get('deepfake_probability', 0)
                confidence = result.get('confidence', 0)
                threat_level = result.get('threat_level', 'unknown')
                detection_method = result.get('detection_method', 'unknown')
                
                logger.info(f"  🎭 GenConViT Result: {deepfake_prob:.2%} deepfake probability")
                logger.info(f"  📊 Confidence: {confidence:.2%}")
                logger.info(f"  ⚠️ Threat Level: {threat_level}")
                logger.info(f"  🔬 Detection Method: {detection_method}")
                
                results.append({
                    "test_case": i,
                    "video_url": test_case["video_url"],
                    "expected": test_case["expected"],
                    "deepfake_probability": deepfake_prob,
                    "confidence": confidence,
                    "threat_level": threat_level,
                    "detection_method": detection_method
                })
                
            else:
                logger.error(f"  ❌ Video API Error: {response.status_code}")
                
        except Exception as e:
            logger.error(f"  ❌ Video Test {i} failed: {e}")
        
        # Longer delay for video processing
        time.sleep(2)
    
    return results

def test_metrics():
    """Test the metrics endpoint"""
    logger.info("📊 Testing AI Metrics...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/api/metrics", timeout=10)
        if response.status_code == 200:
            metrics = response.json()
            logger.info("✅ Metrics retrieved successfully!")
            logger.info(f"  Total Analyses: {metrics.get('total_analyses', 0)}")
            logger.info(f"  Average Accuracy: {metrics.get('average_accuracy', 0):.2%}")
            logger.info(f"  Uptime: {metrics.get('uptime_seconds', 0):.1f} seconds")
            return metrics
        else:
            logger.error(f"❌ Metrics Error: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"❌ Metrics test failed: {e}")
        return None

def generate_report(fraud_results: List[Dict], video_results: List[Dict], metrics: Dict):
    """Generate a test report"""
    logger.info("📋 Generating Test Report...")
    
    report = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "backend_version": "2.0.0-genconvit",
        "fraud_detection_tests": fraud_results,
        "deepfake_detection_tests": video_results,
        "metrics": metrics,
        "summary": {
            "fraud_tests_passed": len([r for r in fraud_results if r.get('fraud_probability', 0) > 0.1]),
            "video_tests_passed": len([r for r in video_results if r.get('deepfake_probability', 0) >= 0]),
            "total_tests": len(fraud_results) + len(video_results)
        }
    }
    
    # Save report
    with open("test_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    logger.info("✅ Test report saved to test_report.json")
    return report

def main():
    """Main test function"""
    logger.info("🛡️ Sentinel Real AI Backend Test Suite")
    logger.info("=" * 60)
    
    # Health check
    if not test_health_check():
        logger.error("❌ Backend is not running. Start it with: python start_ai_backend.py")
        return
    
    # Test fraud detection
    fraud_results = test_fraud_detection()
    
    # Test deepfake detection
    video_results = test_deepfake_detection()
    
    # Test metrics
    metrics = test_metrics()
    
    # Generate report
    report = generate_report(fraud_results, video_results, metrics)
    
    logger.info("🎉 All tests completed!")
    logger.info(f"📊 Summary: {report['summary']['total_tests']} tests run")
    logger.info("📋 Detailed report saved to test_report.json")

if __name__ == "__main__":
    main()
