/**
 * Sentinel Browser Extension - Popup Interface
 */

class SentinelPopup {
  constructor() {
    this.init();
  }

  async init() {
    await this.loadStats();
    this.setupEventListeners();
  }

  async loadStats() {
    try {
      // Get stats from storage
      const stats = await chrome.storage.local.get(['threatsBlocked', 'contentAnalyzed']);
      
      document.getElementById('threats-blocked').textContent = stats.threatsBlocked || 0;
      document.getElementById('content-analyzed').textContent = stats.contentAnalyzed || 0;
      
      // Check if extension is active
      const settings = await chrome.storage.sync.get(['enableThreatOverlay']);
      const isActive = settings.enableThreatOverlay !== false;
      
      const statusElement = document.getElementById('status');
      if (isActive) {
        statusElement.className = 'status active';
        statusElement.innerHTML = '<span style="margin-right: 8px;">●</span><span>Active - Monitoring for threats</span>';
      } else {
        statusElement.className = 'status inactive';
        statusElement.innerHTML = '<span style="margin-right: 8px;">●</span><span>Inactive - Monitoring disabled</span>';
      }
      
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  }

  setupEventListeners() {
    // document.getElementById('settings-btn').addEventListener('click', () => {
    //   chrome.runtime.openOptionsPage();
    // });
  }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new SentinelPopup();
});
