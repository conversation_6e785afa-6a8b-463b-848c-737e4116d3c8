{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest"}, "dependencies": {"@radix-ui/react-slot": "^1.0.2", "@sentinel/shared-types": "file:../../packages/shared-types", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.358.0", "next": "^14.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "swr": "^2.2.5", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@testing-library/react": "^14.2.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.3", "jsdom": "^24.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5", "vitest": "^1.3.1"}}