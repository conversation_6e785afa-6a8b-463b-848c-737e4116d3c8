name: Sentinel Backend CI

on:
  push:
    branches: [ "main", "develop" ]
  pull_request:
    branches: [ "main" ]

jobs:
  backend-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: sentinel_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      working-directory: ./apps/ai-backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov pytest-asyncio

    - name: Set up environment variables
      run: |
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/sentinel_test" >> $GITHUB_ENV
        echo "REDIS_URL=redis://localhost:6379/1" >> $GITHUB_ENV
        echo "API_SECRET_KEY=test-secret-key-for-ci" >> $GITHUB_ENV
        echo "REQUIRE_API_KEY=false" >> $GITHUB_ENV
        echo "DEBUG=true" >> $GITHUB_ENV

    - name: Run tests
      working-directory: ./apps/ai-backend
      run: |
        pytest --cov=. --cov-report=xml --cov-report=term-missing

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        cache: 'poetry'

    - name: Install Python Dependencies
      run: cd services/api && poetry install

    - name: Run Backend Tests
      run: |
        cd services/api
        poetry run pytest

    # Optional: Deployment step
    # - name: Deploy to Vercel
    #   if: github.ref == 'refs/heads/main'
    #   run: npx vercel --prod --token=${{ secrets.VERCEL_TOKEN }}
