"""
Sentinel Rule-Based Backend - Production-Ready Fraud Detection API
Secure, scalable rule-based fraud detection with proper authentication and rate limiting
"""

import asyncio
import logging
import hashlib
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Import configuration and security
from config import get_settings
from auth import get_current_user, require_api_key
from rate_limiter import get_rate_limiter, rate_limit_analysis, rate_limit_general
from security import SecurityHeadersMiddleware, RequestLoggingMiddleware, get_ssrf_protection, get_input_validator

from ai_models.rule_based_text_analyzer import RuleBasedTextAnalyzer
from ai_models.sentiment_analyzer import SentimentAnalyzer
from database.models import AnalysisResult, UserFeedback, AnalysisRequestCreate, AnalysisResultCreate
from database.database import DatabaseManager
from utils.logger import setup_logging
from utils.metrics import MetricsCollector

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Load settings
settings = get_settings()

# Global components
text_analyzer: Optional[RuleBasedTextAnalyzer] = None
sentiment_model: Optional[SentimentAnalyzer] = None
db_manager: Optional[DatabaseManager] = None
metrics: Optional[MetricsCollector] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize and cleanup application components"""
    global text_analyzer, sentiment_model, db_manager, metrics

    logger.info("🔍 Initializing Sentinel Rule-Based Backend...")

    try:
        # Initialize database with proper configuration
        db_manager = DatabaseManager(settings.database_url)
        await db_manager.initialize()

        # Initialize rate limiter
        rate_limiter = await get_rate_limiter()

        # Initialize metrics collector
        metrics = MetricsCollector()

        # Load rule-based text analyzer
        logger.info("📋 Loading rule-based text analyzer...")
        text_analyzer = RuleBasedTextAnalyzer()
        await text_analyzer.load_rules()

        logger.info("💭 Loading sentiment analysis model...")
        sentiment_model = SentimentAnalyzer()
        await sentiment_model.load_model()

        logger.info("✅ All components loaded successfully!")

        yield

    except Exception as e:
        logger.error(f"❌ Failed to initialize backend: {e}")
        raise
    finally:
        logger.info("🔄 Shutting down backend...")
        if db_manager:
            await db_manager.close()

        # Close rate limiter
        rate_limiter = await get_rate_limiter()
        await rate_limiter.close()


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    description="Secure rule-based fraud detection and text analysis for financial content",
    version=settings.app_version,
    lifespan=lifespan,
    debug=settings.debug
)

# Add security middleware
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RequestLoggingMiddleware)

# Add CORS middleware with proper configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["Authorization", "Content-Type"],
)


# Pydantic models for API
class TextAnalysisRequest(BaseModel):
    text: str = Field(..., min_length=1, max_length=10000, description="Text content to analyze")
    url: Optional[str] = Field(None, max_length=2048, description="Source URL (optional)")
    context: Optional[Dict] = Field(None, description="Additional context data")

    class Config:
        schema_extra = {
            "example": {
                "text": "Guaranteed 500% returns in 24 hours! Limited time offer!",
                "url": "https://twitter.com/example/status/123",
                "context": {"platform": "twitter", "user_id": "example_user"}
            }
        }


class FeedbackRequest(BaseModel):
    analysis_id: str = Field(..., description="ID of the analysis to provide feedback on")
    is_correct: bool = Field(..., description="Whether the analysis was correct")
    user_comment: Optional[str] = Field(None, max_length=1000, description="Optional feedback comment")


class TextAnalysisResponse(BaseModel):
    analysis_id: str
    fraud_probability: float = Field(..., ge=0.0, le=1.0)
    confidence: float = Field(..., ge=0.0, le=1.0)
    threat_level: str
    risk_indicators: List[str]
    sentiment_score: float
    processing_time_ms: float
    model_version: str

    class Config:
        schema_extra = {
            "example": {
                "analysis_id": "123e4567-e89b-12d3-a456-426614174000",
                "fraud_probability": 0.85,
                "confidence": 0.92,
                "threat_level": "critical",
                "risk_indicators": [
                    "Rule: Promises guaranteed returns (impossible in real markets)",
                    "Rule: Creates artificial urgency to prevent rational thinking"
                ],
                "sentiment_score": -0.2,
                "processing_time_ms": 45.2,
                "model_version": "1.0.0-rules"
            }
        }


@app.get("/api/health")
async def health_check(
    _: None = Depends(rate_limit_general)
):
    """Health check endpoint with rate limiting"""
    try:
        # Check database health
        db_healthy = db_manager is not None and await db_manager.is_healthy()

        # Check rate limiter health
        rate_limiter = await get_rate_limiter()
        rate_limiter_healthy = rate_limiter.is_connected

        # Overall health status
        is_healthy = (
            text_analyzer is not None and text_analyzer.is_loaded and
            sentiment_model is not None and sentiment_model.is_loaded and
            db_healthy and
            rate_limiter_healthy
        )

        return {
            "status": "healthy" if is_healthy else "degraded",
            "components": {
                "rule_based_analyzer": text_analyzer is not None and text_analyzer.is_loaded,
                "sentiment_analysis": sentiment_model is not None and sentiment_model.is_loaded,
                "database": db_healthy,
                "rate_limiter": rate_limiter_healthy
            },
            "version": settings.app_version,
            "environment": "production" if not settings.debug else "development"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": "Health check failed",
            "version": settings.app_version
        }


@app.post("/api/analyze-text", response_model=TextAnalysisResponse)
async def analyze_text(
    request: TextAnalysisRequest,
    background_tasks: BackgroundTasks,
    user = Depends(get_current_user),
    _: None = Depends(rate_limit_analysis),
    ssrf_protection = Depends(get_ssrf_protection),
    input_validator = Depends(get_input_validator)
):
    """Analyze text for fraud indicators using rule-based analysis"""
    # Check service availability
    if not text_analyzer or not text_analyzer.is_loaded:
        raise HTTPException(status_code=503, detail="Rule-based text analyzer not available")

    if not sentiment_model or not sentiment_model.is_loaded:
        raise HTTPException(status_code=503, detail="Sentiment analysis model not available")

    try:
        # Validate and sanitize input
        clean_text = input_validator.validate_text_content(request.text)

        # Validate URL if provided
        if request.url:
            validated_url = ssrf_protection.validate_url(request.url)
        else:
            validated_url = None

        # Validate platform if provided in context
        platform = None
        if request.context and "platform" in request.context:
            platform = input_validator.validate_platform_name(request.context["platform"])

        logger.info(f"🔍 Analyzing text from {user.name if user else 'anonymous'}: {clean_text[:50]}...")

        # Create content hash for deduplication
        content_hash = hashlib.sha256(clean_text.encode()).hexdigest()

        # Store analysis request
        request_data = AnalysisRequestCreate(
            source_type="text",
            content_hash=content_hash,
            url=validated_url,
            platform=platform,
            context_data=request.context
        )
        request_id = await db_manager.store_analysis_request(request_data)

        # Run rule-based analysis
        analysis_result = await text_analyzer.analyze_text(clean_text, request.context)
        sentiment_result = await sentiment_model.analyze_sentiment(clean_text)

        # Store analysis result
        result_data = AnalysisResultCreate(
            request_id=request_id,
            fraud_probability=analysis_result.fraud_probability,
            confidence=analysis_result.confidence,
            threat_level=analysis_result.threat_level,
            risk_indicators=analysis_result.risk_indicators,
            processing_time_ms=analysis_result.processing_time_ms,
            analyzer_version=text_analyzer.analyzer_version,
            rules_triggered=analysis_result.rule_matches
        )
        result_id = await db_manager.store_analysis_result(result_data)

        # Create response
        response = TextAnalysisResponse(
            analysis_id=result_id,
            fraud_probability=analysis_result.fraud_probability,
            confidence=analysis_result.confidence,
            threat_level=analysis_result.threat_level,
            risk_indicators=analysis_result.risk_indicators,
            sentiment_score=sentiment_result.sentiment_score,
            processing_time_ms=analysis_result.processing_time_ms,
            model_version=text_analyzer.analyzer_version
        )

        # Update metrics
        if metrics:
            metrics.record_analysis("text", analysis_result.threat_level)

        logger.info(f"✅ Text analysis complete: {analysis_result.fraud_probability:.2f} fraud probability")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Text analysis failed: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/api/feedback")
async def submit_feedback(
    request: FeedbackRequest,
    user = Depends(get_current_user),
    _: None = Depends(rate_limit_general),
    input_validator = Depends(get_input_validator)
):
    """Submit feedback on analysis results"""
    try:
        # Validate analysis ID format
        validated_analysis_id = input_validator.validate_analysis_id(request.analysis_id)

        # Store feedback
        from database.models import UserFeedbackCreate
        feedback_data = UserFeedbackCreate(
            result_id=validated_analysis_id,
            is_correct=request.is_correct,
            user_comment=request.user_comment
        )

        feedback_id = await db_manager.store_user_feedback(feedback_data)

        logger.info(f"📝 Feedback received from {user.name if user else 'anonymous'}: {request.is_correct}")

        return {
            "status": "success",
            "feedback_id": feedback_id,
            "message": "Thank you for your feedback!"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Feedback submission failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to submit feedback")


@app.get("/api/stats")
async def get_stats(
    user = Depends(require_api_key),
    _: None = Depends(rate_limit_general)
):
    """Get analysis statistics (requires API key)"""
    try:
        # This would be implemented with proper database queries
        # For now, return basic stats
        return {
            "status": "success",
            "stats": {
                "total_analyses": 0,
                "threat_levels": {
                    "critical": 0,
                    "high": 0,
                    "medium": 0,
                    "low": 0,
                    "minimal": 0
                },
                "accuracy_feedback": {
                    "correct": 0,
                    "incorrect": 0
                }
            },
            "user": {
                "id": str(user.id) if user else None,
                "name": user.name if user else None
            }
        }

    except Exception as e:
        logger.error(f"❌ Stats retrieval failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve stats")


@app.post("/api/feedback")
async def submit_feedback(request: FeedbackRequest):
    """Submit user feedback for model improvement"""
    try:
        if not db_manager:
            raise HTTPException(status_code=503, detail="Database not available")
        
        # Store feedback
        feedback = UserFeedback(
            analysis_id=request.analysis_id,
            is_correct=request.is_correct,
            user_comment=request.user_comment
        )
        
        await db_manager.store_feedback(feedback)
        
        # Trigger model retraining if needed (background task)
        if not request.is_correct:
            logger.info(f"🔄 Incorrect prediction reported for {request.analysis_id}")
            # Could trigger retraining pipeline here
        
        return {"status": "success", "message": "Feedback recorded"}
        
    except Exception as e:
        logger.error(f"❌ Feedback submission failed: {e}")
        raise HTTPException(status_code=500, detail=f"Feedback failed: {str(e)}")


@app.get("/api/metrics")
async def get_metrics():
    """Get AI model performance metrics"""
    if not metrics:
        raise HTTPException(status_code=503, detail="Metrics not available")
    
    return await metrics.get_summary()


async def store_analysis_result(analysis_type: str, request_data: dict, response_data: dict):
    """Background task to store analysis results"""
    try:
        if db_manager:
            result = AnalysisResult(
                analysis_type=analysis_type,
                request_data=request_data,
                response_data=response_data
            )
            await db_manager.store_analysis(result)
    except Exception as e:
        logger.error(f"Failed to store analysis result: {e}")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
