"""
Security utilities for Sentinel Backend
Includes SSRF prevention, input validation, and security headers
"""

import ipaddress
import logging
import time
from urllib.parse import urlparse
from typing import List, Optional
import re
from fastapi import HTTPException, Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware

from config import get_settings

logger = logging.getLogger(__name__)


class SSRFProtection:
    """Server-Side Request Forgery protection"""
    
    def __init__(self):
        self.settings = get_settings()
        self.blocked_networks = [
            ipaddress.ip_network(network) for network in self.settings.blocked_ip_ranges
        ]
    
    def is_ip_blocked(self, ip_str: str) -> bool:
        """Check if IP address is in blocked ranges"""
        try:
            ip = ipaddress.ip_address(ip_str)
            
            # Block private/internal IP ranges
            for network in self.blocked_networks:
                if ip in network:
                    return True
            
            # Block localhost variations
            if ip.is_loopback or ip.is_private or ip.is_reserved:
                return True
            
            return False
            
        except ValueError:
            # Invalid IP address
            return True
    
    def is_domain_allowed(self, domain: str) -> bool:
        """Check if domain is in allowed list"""
        domain = domain.lower()
        
        # Check exact match
        if domain in self.settings.allowed_domains:
            return True
        
        # Check subdomain match
        for allowed_domain in self.settings.allowed_domains:
            if domain.endswith(f".{allowed_domain}"):
                return True
        
        return False
    
    def validate_url(self, url: str) -> str:
        """Validate URL for SSRF protection"""
        try:
            parsed = urlparse(url)
            
            # Must be HTTP or HTTPS
            if parsed.scheme not in ['http', 'https']:
                raise HTTPException(
                    status_code=400,
                    detail="Only HTTP and HTTPS URLs are allowed"
                )
            
            # Must have a hostname
            if not parsed.hostname:
                raise HTTPException(
                    status_code=400,
                    detail="URL must have a valid hostname"
                )
            
            # Check if domain is allowed
            if not self.is_domain_allowed(parsed.hostname):
                raise HTTPException(
                    status_code=400,
                    detail=f"Domain '{parsed.hostname}' is not in the allowed list"
                )
            
            # Resolve hostname to IP and check if blocked
            try:
                import socket
                ip = socket.gethostbyname(parsed.hostname)
                if self.is_ip_blocked(ip):
                    raise HTTPException(
                        status_code=400,
                        detail=f"URL resolves to blocked IP address: {ip}"
                    )
            except socket.gaierror:
                raise HTTPException(
                    status_code=400,
                    detail="Unable to resolve hostname"
                )
            
            return url
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"URL validation error: {e}")
            raise HTTPException(
                status_code=400,
                detail="Invalid URL format"
            )


class InputValidator:
    """Input validation utilities"""
    
    @staticmethod
    def validate_text_content(text: str) -> str:
        """Validate and sanitize text content"""
        if not isinstance(text, str):
            raise HTTPException(
                status_code=400,
                detail="Text content must be a string"
            )
        
        # Remove null bytes and control characters
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # Limit length
        settings = get_settings()
        if len(text) > settings.max_text_length:
            raise HTTPException(
                status_code=400,
                detail=f"Text too long. Maximum {settings.max_text_length} characters allowed"
            )
        
        # Must not be empty after cleaning
        if not text.strip():
            raise HTTPException(
                status_code=400,
                detail="Text content cannot be empty"
            )
        
        return text.strip()
    
    @staticmethod
    def validate_platform_name(platform: Optional[str]) -> Optional[str]:
        """Validate platform name"""
        if not platform:
            return None
        
        # Allow only alphanumeric and common separators
        if not re.match(r'^[a-zA-Z0-9_.-]+$', platform):
            raise HTTPException(
                status_code=400,
                detail="Invalid platform name format"
            )
        
        return platform.lower()
    
    @staticmethod
    def validate_analysis_id(analysis_id: str) -> str:
        """Validate analysis ID format"""
        # Should be a UUID format
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        if not re.match(uuid_pattern, analysis_id, re.IGNORECASE):
            raise HTTPException(
                status_code=400,
                detail="Invalid analysis ID format"
            )
        
        return analysis_id.lower()


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to all responses"""
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        # Remove server header
        response.headers.pop("server", None)
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Log all requests for security monitoring"""
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Log request
        client_ip = request.client.host if request.client else "unknown"
        logger.info(f"Request: {request.method} {request.url.path} from {client_ip}")
        
        response = await call_next(request)
        
        # Log response
        process_time = time.time() - start_time
        logger.info(f"Response: {response.status_code} in {process_time:.3f}s")
        
        return response


# Global instances
ssrf_protection = SSRFProtection()
input_validator = InputValidator()


def get_ssrf_protection() -> SSRFProtection:
    """Get SSRF protection instance"""
    return ssrf_protection


def get_input_validator() -> InputValidator:
    """Get input validator instance"""
    return input_validator
