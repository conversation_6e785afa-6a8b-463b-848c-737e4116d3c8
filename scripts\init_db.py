#!/usr/bin/env python3
"""
Initialize Sentinel Database with Real Schema
Creates all necessary tables and indexes for production use.
"""

import asyncio
import asyncpg
import os
from datetime import datetime

async def create_database_schema():
    """Create the complete database schema."""
    
    # Use local database for development
    database_url = os.getenv("SUPABASE_DB_URL", "postgresql://postgres:password@localhost:5432/sentinel_dev")
    
    try:
        conn = await asyncpg.connect(database_url)
        
        # Create threat_feed table
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS threat_feed (
                id SERIAL PRIMARY KEY,
                content_text TEXT NOT NULL,
                source_platform VARCHAR(50) NOT NULL,
                source_url TEXT,
                timestamp TIMESTAMPTZ DEFAULT NOW(),
                sentinel_score INTEGER NOT NULL,
                risk_factors JSONB DEFAULT '[]',
                confidence FLOAT DEFAULT 0.0,
                fraud_risk FLOAT DEFAULT 0.0,
                sentiment VARCHAR(20) DEFAULT 'neutral',
                analysis_timestamp TIMESTAMPTZ DEFAULT NOW(),
                metadata JSONB DEFAULT '{}',
                content_id VARCHAR(255) UNIQUE,
                ai_enhanced BOOLEAN DEFAULT FALSE,
                detected_patterns JSONB DEFAULT '[]',
                emotional_intensity FLOAT DEFAULT 0.0,
                vector_embedding JSONB DEFAULT NULL,
                processing_time_ms INTEGER DEFAULT 0,
                model_version VARCHAR(100) DEFAULT 'unknown'
            );
        """)
        
        # Create indexes for performance
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_threat_feed_timestamp
            ON threat_feed(timestamp DESC);
        """)
        
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_threat_feed_score
            ON threat_feed(sentinel_score);
        """)
        
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_threat_feed_platform
            ON threat_feed(source_platform);
        """)
        
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_threat_feed_ai_enhanced
            ON threat_feed(ai_enhanced);
        """)
        
        # Create video_analysis table for deepfake detection
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS video_analysis (
                id SERIAL PRIMARY KEY,
                video_url TEXT NOT NULL,
                deepfake_probability FLOAT DEFAULT 0.0,
                confidence FLOAT DEFAULT 0.0,
                analysis_method VARCHAR(100) DEFAULT 'unknown',
                frames_analyzed INTEGER DEFAULT 0,
                faces_detected INTEGER DEFAULT 0,
                processing_time_ms INTEGER DEFAULT 0,
                risk_indicators JSONB DEFAULT '[]',
                threat_level VARCHAR(20) DEFAULT 'low',
                requires_warning BOOLEAN DEFAULT FALSE,
                analysis_timestamp TIMESTAMPTZ DEFAULT NOW(),
                model_version VARCHAR(100) DEFAULT 'unknown'
            );
        """)
        
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_video_analysis_timestamp
            ON video_analysis(analysis_timestamp DESC);
        """)
        
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_video_analysis_probability
            ON video_analysis(deepfake_probability DESC);
        """)
        
        # Insert sample data for testing
        await conn.execute("""
            INSERT INTO threat_feed (
                content_text, source_platform, sentinel_score, risk_factors, 
                confidence, fraud_risk, sentiment, content_id, ai_enhanced
            ) VALUES 
            ('🚨 GUARANTEED 500% returns in 24 hours! Secret crypto algorithm!', 'Twitter', 15, 
             '["guaranteed_returns", "urgency_pressure", "unrealistic_claims"]', 0.95, 0.9, 'overly_positive', 
             'sample_1', true),
            ('Market analysis shows steady growth in tech sector. Diversified portfolio recommended.', 'Reddit', 85,
             '[]', 0.8, 0.1, 'neutral', 'sample_2', true),
            ('URGENT: Limited spots available for exclusive investment opportunity!', 'Telegram', 25,
             '["urgency_pressure", "insider_claims"]', 0.88, 0.75, 'positive', 'sample_3', true)
            ON CONFLICT (content_id) DO NOTHING;
        """)
        
        await conn.close()
        print("✅ Database schema created successfully!")
        print("✅ Sample data inserted for testing")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(create_database_schema())
