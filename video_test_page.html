<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentinel Video Analysis Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .video-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }
        video {
            width: 100%;
            max-width: 600px;
            border-radius: 8px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .test-info {
            background: #fff3e0;
            padding: 16px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <h1>🛡️ Sentinel Video Analysis Test Page</h1>
    
    <div class="instructions">
        <h3>📋 Testing Instructions</h3>
        <ol>
            <li><strong>Install Extension:</strong> Make sure Sentinel extension is loaded and active</li>
            <li><strong>Look for Buttons:</strong> Red "🛡️ Analyze" buttons should appear on each video</li>
            <li><strong>Click to Test:</strong> Click any analyze button to test deepfake detection</li>
            <li><strong>Watch Process:</strong> Button should change to "🔴 Recording..." then "🤖 Analyzing..."</li>
            <li><strong>View Results:</strong> Professional popup should show deepfake probability</li>
        </ol>
    </div>

    <div class="video-container">
        <h3>🎬 Test Video 1: Sample Content</h3>
        <div class="test-info">
            <strong>Expected Result:</strong> Low deepfake probability (10-30%)
        </div>
        <video controls>
            <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
            <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>

    <div class="video-container">
        <h3>🎭 Test Video 2: Suspicious URL Pattern</h3>
        <div class="test-info">
            <strong>Expected Result:</strong> High deepfake probability (70-90%) due to URL pattern
        </div>
        <video controls>
            <source src="https://deepfake-generator.com/sample-fake-video.mp4" type="video/mp4">
            <source src="https://synthetic-media.ai/test-deepfake.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>

    <div class="video-container">
        <h3>🤖 Test Video 3: AI-Generated Content</h3>
        <div class="test-info">
            <strong>Expected Result:</strong> Medium-high deepfake probability (40-70%)
        </div>
        <video controls>
            <source src="https://ai-generated-content.com/synthetic-video.mp4" type="video/mp4">
            <source src="https://artificial-media.net/manipulated-content.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    </div>

    <div class="video-container">
        <h3>📺 Test Video 4: Embedded YouTube-style</h3>
        <div class="test-info">
            <strong>Expected Result:</strong> Variable probability based on URL analysis
        </div>
        <div style="position: relative; background: #000; border-radius: 8px;">
            <video controls style="background: #000;">
                <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
    </div>

    <div style="background: white; padding: 20px; border-radius: 12px; margin-top: 30px;">
        <h3>🔍 Debugging Information</h3>
        <p><strong>Page URL:</strong> <code id="current-url"></code></p>
        <p><strong>Videos Found:</strong> <span id="video-count">Calculating...</span></p>
        <p><strong>Extension Status:</strong> <span id="extension-status">Checking...</span></p>
        
        <button onclick="checkExtensionStatus()" style="
            background: #3b82f6; color: white; border: none; 
            padding: 8px 16px; border-radius: 6px; cursor: pointer; margin: 5px;
        ">
            🔄 Refresh Status
        </button>
        
        <button onclick="forceAddButtons()" style="
            background: #10b981; color: white; border: none; 
            padding: 8px 16px; border-radius: 6px; cursor: pointer; margin: 5px;
        ">
            🛡️ Force Add Buttons
        </button>
    </div>

    <script>
        // Update page info
        document.getElementById('current-url').textContent = window.location.href;
        
        function checkExtensionStatus() {
            const videos = document.querySelectorAll('video');
            document.getElementById('video-count').textContent = videos.length;
            
            const buttonsFound = document.querySelectorAll('.sentinel-video-button').length;
            const extensionActive = window.sentinelInstance ? 'Active' : 'Not Found';
            
            document.getElementById('extension-status').innerHTML = `
                ${extensionActive} | 
                Buttons: ${buttonsFound}/${videos.length} | 
                Console: ${window.sentinelInstance ? '✅' : '❌'}
            `;
        }
        
        function forceAddButtons() {
            if (window.sentinelInstance) {
                console.log('🛡️ Forcing button addition...');
                window.sentinelInstance.addVideoButtons();
                setTimeout(checkExtensionStatus, 500);
            } else {
                alert('Sentinel extension not found. Please reload the extension.');
            }
        }
        
        // Auto-check status
        setTimeout(checkExtensionStatus, 2000);
        setInterval(checkExtensionStatus, 5000);
        
        // Log video events
        document.querySelectorAll('video').forEach((video, index) => {
            video.addEventListener('loadeddata', () => {
                console.log(`🎬 Video ${index + 1} loaded:`, video.src);
            });
        });
    </script>
</body>
</html>
