events {
    worker_connections 1024;
}

http {
    upstream sentinel_api {
        server api:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=health:10m rate=1r/s;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    server {
        listen 80;
        server_name _;

        # Security
        server_tokens off;
        
        # Health check endpoint (less restrictive rate limiting)
        location /api/health {
            limit_req zone=health burst=5 nodelay;
            proxy_pass http://sentinel_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API endpoints (stricter rate limiting)
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://sentinel_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeout settings
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Block access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Default location
        location / {
            return 404;
        }
    }

    # HTTPS configuration (uncomment and configure for production)
    # server {
    #     listen 443 ssl http2;
    #     server_name yourdomain.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     # SSL configuration
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     # Same location blocks as HTTP server
    #     location /api/health {
    #         limit_req zone=health burst=5 nodelay;
    #         proxy_pass http://sentinel_api;
    #         # ... same proxy settings
    #     }
    #     
    #     location /api/ {
    #         limit_req zone=api burst=20 nodelay;
    #         proxy_pass http://sentinel_api;
    #         # ... same proxy settings
    #     }
    # }
}
