"""
Tests for security components
"""

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from security import SSR<PERSON><PERSON><PERSON>, Input<PERSON><PERSON><PERSON>tor
from auth import generate_api_key, hash_api_key


def test_ssrf_protection_blocked_ips():
    """Test SSRF protection blocks internal IPs"""
    ssrf = SSRFProtection()
    
    blocked_ips = [
        "127.0.0.1",
        "********",
        "**********",
        "***********",
        "***************",  # AWS metadata
        "::1"  # IPv6 localhost
    ]
    
    for ip in blocked_ips:
        assert ssrf.is_ip_blocked(ip), f"Should block IP: {ip}"


def test_ssrf_protection_allowed_ips():
    """Test SSRF protection allows public IPs"""
    ssrf = SSRFProtection()
    
    allowed_ips = [
        "*******",
        "*******",
        "**************"
    ]
    
    for ip in allowed_ips:
        assert not ssrf.is_ip_blocked(ip), f"Should allow IP: {ip}"


def test_ssrf_domain_validation():
    """Test domain validation"""
    ssrf = SSRFProtection()
    
    # Test allowed domains
    allowed_domains = [
        "twitter.com",
        "www.twitter.com",
        "mobile.twitter.com",
        "reddit.com",
        "old.reddit.com"
    ]
    
    for domain in allowed_domains:
        assert ssrf.is_domain_allowed(domain), f"Should allow domain: {domain}"
    
    # Test blocked domains
    blocked_domains = [
        "evil.com",
        "localhost",
        "internal.company.com"
    ]
    
    for domain in blocked_domains:
        assert not ssrf.is_domain_allowed(domain), f"Should block domain: {domain}"


def test_ssrf_url_validation():
    """Test URL validation"""
    ssrf = SSRFProtection()
    
    # Test invalid schemes
    with pytest.raises(HTTPException):
        ssrf.validate_url("ftp://example.com")
    
    with pytest.raises(HTTPException):
        ssrf.validate_url("javascript:alert('xss')")
    
    with pytest.raises(HTTPException):
        ssrf.validate_url("file:///etc/passwd")
    
    # Test missing hostname
    with pytest.raises(HTTPException):
        ssrf.validate_url("https://")
    
    # Test blocked domain
    with pytest.raises(HTTPException):
        ssrf.validate_url("https://evil.com/path")


def test_input_validator_text_content():
    """Test text content validation"""
    validator = InputValidator()
    
    # Valid text
    valid_text = "This is a normal text message."
    result = validator.validate_text_content(valid_text)
    assert result == valid_text.strip()
    
    # Text with control characters
    dirty_text = "Text with\x00null\x08bytes"
    clean_text = validator.validate_text_content(dirty_text)
    assert "\x00" not in clean_text
    assert "\x08" not in clean_text
    
    # Empty text should raise error
    with pytest.raises(HTTPException):
        validator.validate_text_content("")
    
    with pytest.raises(HTTPException):
        validator.validate_text_content("   \n\t   ")
    
    # Non-string input should raise error
    with pytest.raises(HTTPException):
        validator.validate_text_content(123)


def test_input_validator_platform_name():
    """Test platform name validation"""
    validator = InputValidator()
    
    # Valid platform names
    valid_platforms = ["twitter", "reddit", "youtube", "linked-in", "face_book"]
    for platform in valid_platforms:
        result = validator.validate_platform_name(platform)
        assert result == platform.lower()
    
    # Invalid platform names
    invalid_platforms = ["twitter!", "reddit@home", "you tube", "linked/in"]
    for platform in invalid_platforms:
        with pytest.raises(HTTPException):
            validator.validate_platform_name(platform)
    
    # None should return None
    assert validator.validate_platform_name(None) is None


def test_input_validator_analysis_id():
    """Test analysis ID validation"""
    validator = InputValidator()
    
    # Valid UUID
    valid_uuid = "123e4567-e89b-12d3-a456-************"
    result = validator.validate_analysis_id(valid_uuid)
    assert result == valid_uuid.lower()
    
    # Invalid UUIDs
    invalid_uuids = [
        "not-a-uuid",
        "123e4567-e89b-12d3-a456",  # Too short
        "123e4567-e89b-12d3-a456-************-extra",  # Too long
        "ggge4567-e89b-12d3-a456-************"  # Invalid characters
    ]
    
    for invalid_uuid in invalid_uuids:
        with pytest.raises(HTTPException):
            validator.validate_analysis_id(invalid_uuid)


def test_api_key_generation():
    """Test API key generation"""
    # Generate multiple keys
    keys = [generate_api_key() for _ in range(10)]
    
    # All keys should be unique
    assert len(set(keys)) == len(keys)
    
    # All keys should be reasonable length
    for key in keys:
        assert len(key) >= 32
        assert isinstance(key, str)


def test_api_key_hashing():
    """Test API key hashing"""
    api_key = "test-api-key-123"
    
    # Hash should be consistent
    hash1 = hash_api_key(api_key)
    hash2 = hash_api_key(api_key)
    assert hash1 == hash2
    
    # Different keys should have different hashes
    different_key = "different-api-key-456"
    hash3 = hash_api_key(different_key)
    assert hash1 != hash3
    
    # Hash should be hex string
    assert all(c in '0123456789abcdef' for c in hash1)


def test_content_length_limits():
    """Test content length validation"""
    validator = InputValidator()
    
    # Text at limit should pass
    max_length = 10000
    text_at_limit = "x" * max_length
    result = validator.validate_text_content(text_at_limit)
    assert len(result) == max_length
    
    # Text over limit should fail
    text_over_limit = "x" * (max_length + 1)
    with pytest.raises(HTTPException) as exc_info:
        validator.validate_text_content(text_over_limit)
    assert exc_info.value.status_code == 400


def test_url_length_limits():
    """Test URL length validation"""
    from auth import validate_url_format
    
    # URL at limit should pass (this would need to be implemented)
    max_length = 2048
    base_url = "https://example.com/"
    long_path = "x" * (max_length - len(base_url) - 1)
    url_at_limit = base_url + long_path
    
    # This test assumes validate_url_format exists and checks length
    try:
        validate_url_format(url_at_limit)
    except HTTPException as e:
        # If it fails, it should be due to length, not format
        assert "too long" in str(e.detail).lower()


def test_malicious_input_handling():
    """Test handling of malicious input"""
    validator = InputValidator()
    
    # SQL injection attempts
    sql_injection = "'; DROP TABLE users; --"
    result = validator.validate_text_content(sql_injection)
    # Should not raise error, just clean the text
    assert isinstance(result, str)
    
    # XSS attempts
    xss_attempt = "<script>alert('xss')</script>"
    result = validator.validate_text_content(xss_attempt)
    # Should not raise error, just clean the text
    assert isinstance(result, str)
    
    # Path traversal attempts
    path_traversal = "../../../etc/passwd"
    result = validator.validate_text_content(path_traversal)
    # Should not raise error, just clean the text
    assert isinstance(result, str)
