"""
Rate limiting for Sentinel Backend using Redis
"""

import time
import logging
from typing import Optional
import redis.asyncio as redis
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Depends
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from config import get_settings
from database.models import User

logger = logging.getLogger(__name__)


class RateLimiter:
    """Redis-based rate limiter"""
    
    def __init__(self, redis_url: str):
        self.redis_client = None
        self.redis_url = redis_url
        self.is_connected = False
    
    async def connect(self):
        """Connect to Redis"""
        try:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
            # Test connection
            await self.redis_client.ping()
            self.is_connected = True
            logger.info("✅ Connected to Redis for rate limiting")
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            self.is_connected = False
    
    async def close(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()
            self.is_connected = False
    
    async def is_rate_limited(self, key: str, limit: int, window: int) -> tuple[bool, dict]:
        """
        Check if a key is rate limited
        
        Args:
            key: Unique identifier (IP, user ID, etc.)
            limit: Maximum number of requests
            window: Time window in seconds
            
        Returns:
            (is_limited, info_dict)
        """
        if not self.is_connected:
            # If Redis is not available, allow requests but log warning
            logger.warning("Redis not available, rate limiting disabled")
            return False, {"remaining": limit, "reset_time": int(time.time()) + window}
        
        try:
            current_time = int(time.time())
            window_start = current_time - window
            
            # Use Redis sorted set to track requests in time window
            pipe = self.redis_client.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, window_start)
            
            # Count current requests
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(key, window)
            
            results = await pipe.execute()
            current_count = results[1]
            
            remaining = max(0, limit - current_count - 1)
            reset_time = current_time + window
            
            is_limited = current_count >= limit
            
            return is_limited, {
                "remaining": remaining,
                "reset_time": reset_time,
                "current_count": current_count + 1
            }
            
        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # On error, allow request but log
            return False, {"remaining": limit, "reset_time": int(time.time()) + window}


# Global rate limiter instance
rate_limiter = None


async def get_rate_limiter() -> RateLimiter:
    """Get rate limiter instance"""
    global rate_limiter
    if not rate_limiter:
        settings = get_settings()
        rate_limiter = RateLimiter(settings.redis_url)
        await rate_limiter.connect()
    return rate_limiter


def get_client_id(request: Request, user: Optional[User] = None) -> str:
    """Get unique client identifier for rate limiting"""
    if user:
        return f"user:{user.id}"
    else:
        # Use IP address for anonymous requests
        client_ip = get_remote_address(request)
        return f"ip:{client_ip}"


async def check_rate_limit(
    request: Request,
    user: Optional[User] = None,
    endpoint: str = "default"
) -> None:
    """Check rate limit for request"""
    settings = get_settings()
    limiter = await get_rate_limiter()
    
    client_id = get_client_id(request, user)
    
    # Check per-minute limit
    minute_key = f"rate_limit:{endpoint}:{client_id}:minute"
    is_limited_minute, minute_info = await limiter.is_rate_limited(
        minute_key, settings.rate_limit_per_minute, 60
    )
    
    if is_limited_minute:
        raise HTTPException(
            status_code=429,
            detail={
                "error": "Rate limit exceeded",
                "limit_type": "per_minute",
                "limit": settings.rate_limit_per_minute,
                "remaining": minute_info["remaining"],
                "reset_time": minute_info["reset_time"]
            }
        )
    
    # Check per-hour limit
    hour_key = f"rate_limit:{endpoint}:{client_id}:hour"
    is_limited_hour, hour_info = await limiter.is_rate_limited(
        hour_key, settings.rate_limit_per_hour, 3600
    )
    
    if is_limited_hour:
        raise HTTPException(
            status_code=429,
            detail={
                "error": "Rate limit exceeded",
                "limit_type": "per_hour",
                "limit": settings.rate_limit_per_hour,
                "remaining": hour_info["remaining"],
                "reset_time": hour_info["reset_time"]
            }
        )
    
    # Add rate limit headers to response (this would be done in middleware)
    logger.debug(f"Rate limit check passed for {client_id} on {endpoint}")


# Dependency for rate limiting specific endpoints
async def rate_limit_analysis(
    request: Request,
    user: Optional[User] = Depends()
) -> None:
    """Rate limit for analysis endpoints"""
    await check_rate_limit(request, user, "analysis")


async def rate_limit_general(
    request: Request,
    user: Optional[User] = Depends()
) -> None:
    """Rate limit for general endpoints"""
    await check_rate_limit(request, user, "general")


# Slowapi limiter for simple rate limiting (fallback)
def get_slowapi_limiter():
    """Get slowapi limiter instance"""
    return Limiter(
        key_func=get_remote_address,
        default_limits=["100/minute", "1000/hour"]
    )


# Exception handler for rate limit exceeded
async def rate_limit_handler(request: Request, exc: RateLimitExceeded):
    """Handle rate limit exceeded exceptions"""
    response = _rate_limit_exceeded_handler(request, exc)
    logger.warning(f"Rate limit exceeded for {get_remote_address(request)}")
    return response
