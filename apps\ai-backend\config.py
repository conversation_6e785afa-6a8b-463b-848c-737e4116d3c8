"""
Configuration management for Sentinel Backend
Uses Pydantic BaseSettings to load configuration from environment variables
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field, validator


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # Application
    app_name: str = Field(default="Sentinel Rule-Based Backend", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Database
    database_url: str = Field(..., env="DATABASE_URL")
    database_echo: bool = Field(default=False, env="DATABASE_ECHO")
    
    # Redis (for caching and rate limiting)
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # Security
    api_secret_key: str = Field(..., env="API_SECRET_KEY")
    cors_origins: List[str] = Field(default=["http://localhost:3000"], env="CORS_ORIGINS")
    allowed_hosts: List[str] = Field(default=["localhost", "127.0.0.1"], env="ALLOWED_HOSTS")
    
    # Rate Limiting
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")
    rate_limit_per_hour: int = Field(default=1000, env="RATE_LIMIT_PER_HOUR")
    
    # API Keys
    default_api_key: Optional[str] = Field(default=None, env="DEFAULT_API_KEY")
    require_api_key: bool = Field(default=True, env="REQUIRE_API_KEY")
    
    # Content Analysis
    max_text_length: int = Field(default=10000, env="MAX_TEXT_LENGTH")
    max_url_length: int = Field(default=2048, env="MAX_URL_LENGTH")
    
    # SSRF Prevention
    allowed_domains: List[str] = Field(
        default=["twitter.com", "reddit.com", "youtube.com", "linkedin.com"],
        env="ALLOWED_DOMAINS"
    )
    blocked_ip_ranges: List[str] = Field(
        default=["*********/8", "10.0.0.0/8", "**********/12", "***********/16"],
        env="BLOCKED_IP_RANGES"
    )
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from comma-separated string"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("allowed_hosts", pre=True)
    def parse_allowed_hosts(cls, v):
        """Parse allowed hosts from comma-separated string"""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("allowed_domains", pre=True)
    def parse_allowed_domains(cls, v):
        """Parse allowed domains from comma-separated string"""
        if isinstance(v, str):
            return [domain.strip() for domain in v.split(",")]
        return v
    
    @validator("blocked_ip_ranges", pre=True)
    def parse_blocked_ip_ranges(cls, v):
        """Parse blocked IP ranges from comma-separated string"""
        if isinstance(v, str):
            return [ip_range.strip() for ip_range in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings
