# 🛡️ Sentinel V1 - Rule-Based Fraud Detection

**Browser Extension and API for Financial Content Analysis**

Sentinel is a browser extension and API that analyzes text content against a set of configurable rules to identify potential financial fraud indicators. Built with a focus on reliability, security, and honest capabilities.

## 🎯 Current Features

### 🔍 Rule-Based Text Analysis
- **Pattern matching** against known fraud indicators
- **Configurable rule sets** for different types of financial scams
- **Threat level scoring** (Critical, High, Medium, Low, Minimal)
- **Risk indicator reporting** with detailed explanations

### 🌐 Browser Extension
- **Real-time content scanning** on social media platforms
- **Visual threat indicators** overlaid on suspicious content
- **Click-through analysis details** for user education
- **Statistics tracking** for threats detected and content analyzed

### 🔧 Production-Ready Backend
- **FastAPI-based REST API** with proper error handling
- **PostgreSQL database** for persistent storage
- **Redis caching** for performance optimization
- **Docker containerization** for consistent deployment
- **Comprehensive logging** and health monitoring

## 🏗️ Architecture Principles

This system is built on solid engineering principles:

### Principle I: Truth in Advertising
- All capabilities are honestly documented
- No misleading claims about AI or machine learning
- Clear distinction between current and future features

### Principle II: Security First
- API key authentication for all endpoints
- Rate limiting to prevent abuse
- Input validation using Pydantic models
- CORS configuration for browser extension security

### Principle III: Maintainable Code
- Clean, well-documented codebase
- Comprehensive test coverage
- Docker-based development environment
- Consistent code formatting and linting

### Principle IV: Scalable Foundation
- Database-backed persistence
- Async I/O throughout the backend
- Containerized deployment ready
- Monitoring and observability built-in

## 🚀 Developer Setup

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for web development)
- Python 3.11+ (for backend development)

### Quick Start with Docker

1. **Clone and Start**
   ```bash
   git clone <your-repo>
   cd sentinal
   docker-compose up -d
   ```

2. **Verify Services**
   ```bash
   # Check API health
   curl http://localhost:8000/api/health

   # Check web application
   open http://localhost:3000
   ```

3. **Load Browser Extension**
   - Open Chrome/Edge browser
   - Go to Extensions → Developer Mode
   - Click "Load unpacked" and select `apps/browser-extension`

### Local Development (without Docker)

1. **Backend Setup**
   ```bash
   cd apps/ai-backend
   pip install -r requirements.txt

   # Set up environment variables
   export DATABASE_URL="postgresql://user:pass@localhost/sentinel"
   export REDIS_URL="redis://localhost:6379/0"

   # Run database migrations
   alembic upgrade head

   # Start the API
   uvicorn main:app --reload
   ```

2. **Frontend Setup**
   ```bash
   cd apps/web
   npm install
   npm run dev
   ```

## 🧪 Testing

### Run Tests
```bash
# Backend tests
cd apps/ai-backend
pytest

# Frontend tests
cd apps/web
npm test

# Integration tests with Docker
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 📊 API Documentation

### Health Check
```bash
GET /api/health
```

### Text Analysis
```bash
POST /api/analyze-text
Content-Type: application/json

{
  "text": "Guaranteed 500% returns in 24 hours!",
  "url": "https://example.com",
  "context": {
    "platform": "twitter"
  }
}
```

Response:
```json
{
  "analysis_id": "uuid",
  "fraud_probability": 0.85,
  "confidence": 0.92,
  "threat_level": "critical",
  "risk_indicators": [
    "Rule: Promises guaranteed returns (impossible in real markets)",
    "Rule: Claims unrealistic returns that violate market fundamentals"
  ],
  "sentiment_score": -0.2,
  "processing_time_ms": 45.2,
  "model_version": "1.0.0-rules"
}
```

## 🔧 Configuration

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://sentinel:password@localhost:5432/sentinel
REDIS_URL=redis://localhost:6379/0

# Security
API_SECRET_KEY=your-secret-key-here
CORS_ORIGINS=http://localhost:3000,chrome-extension://

# Logging
LOG_LEVEL=INFO
```

### Rule Configuration
Rules are defined in `apps/ai-backend/ai_models/rule_based_text_analyzer.py`. Future versions will support external rule configuration files.

## 🚀 Deployment

### Production Deployment
```bash
# Build and deploy with Docker
docker-compose -f docker-compose.prod.yml up -d

# Or deploy individual services
docker build -t sentinel-api apps/ai-backend
docker run -p 8000:8000 sentinel-api
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Ensure tests pass: `pytest` and `npm test`
5. Format code: `black .` and `npm run format`
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- 📖 Documentation: Check the `/docs` directory
- 🐛 Issues: Create an issue on GitHub
- 💬 Discussions: Use GitHub Discussions for questions

---

**🛡️ Sentinel V1 - Honest, Reliable Fraud Detection**

*Built with solid engineering principles and honest capabilities.*
