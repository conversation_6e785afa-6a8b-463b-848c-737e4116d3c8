#!/usr/bin/env python3
"""
Enterprise Sentinel Seeding Script
Demonstrates the secure, asynchronous ingestion pipeline.
"""

import asyncio
import aiohttp
import os
from typing import List, Dict

# Enhanced sample threat data for winning demonstration
SAMPLE_THREATS = [
    # High-Risk Fraudulent Content
    {
        "content_text": "🚨 URGENT: Guaranteed 500% returns in 24 hours! Limited spots available. DM me now for exclusive access to this secret trading algorithm! Don't miss this once-in-a-lifetime opportunity!",
        "source_platform": "Telegram",
        "source_url": "https://t.me/crypto_pumps_vip/1234",
        "metadata": {"channel": "@crypto_pumps_vip", "followers": 15000}
    },
    {
        "content_text": "BREAKING: Insider information on RELIANCE stock! Buy immediately before tomorrow's announcement. 100% confirmed profit opportunity! My sources never fail. Act NOW!",
        "source_platform": "Telegram",
        "source_url": "https://t.me/stock_tips_india/5678",
        "metadata": {"channel": "@stock_tips_india", "followers": 8500}
    },
    {
        "content_text": "🔥 MASSIVE PUMP INCOMING: $ACME about to moon! My insider sources confirm big announcement coming. Get in NOW before it's too late! 1000% gains guaranteed! #StockTip #Investment #PumpAlert",
        "source_platform": "Twitter",
        "source_url": "https://twitter.com/fake_guru/status/123456789",
        "metadata": {"user": "@fake_guru", "followers": 25000}
    },
    {
        "content_text": "Risk-free investment opportunity! Double your money in one week with our proven AI trading system. Limited time offer - only 10 spots left! Join thousands of successful investors!",
        "source_platform": "Reddit",
        "source_url": "https://reddit.com/r/investing/fake_post_123",
        "metadata": {"subreddit": "r/investing", "upvotes": 45}
    },
    {
        "content_text": "Secret crypto strategy revealed! Turn ₹10,000 into ₹1,00,000 in just 7 days. Exclusive WhatsApp group access for first 50 people. Don't let this opportunity slip away!",
        "source_platform": "Discord",
        "source_url": "https://discord.gg/fake_crypto_server",
        "metadata": {"server": "Crypto Millionaires", "members": 12000}
    },

    # Medium-Risk Suspicious Content
    {
        "content_text": "Amazing investment opportunity in emerging tech stocks! Our analysts predict significant growth potential. Contact us for detailed portfolio recommendations.",
        "source_platform": "News",
        "source_url": "https://suspicious-finance-blog.com/tech-stocks-boom",
        "metadata": {"source": "TechFinance Blog", "category": "investment"}
    },
    {
        "content_text": "Hot tip: TATA stocks showing strong momentum. Technical analysis suggests potential breakout. Consider adding to your portfolio for good returns.",
        "source_platform": "Twitter",
        "source_url": "https://twitter.com/market_analyst_pro/status/987654321",
        "metadata": {"user": "@market_analyst_pro", "followers": 5000}
    },

    # Low-Risk Legitimate Content
    {
        "content_text": "RELIANCE INDUSTRIES LTD announces board meeting scheduled for next week to discuss quarterly results and dividend declaration. Meeting agenda includes review of financial performance and strategic initiatives.",
        "source_platform": "BSE",
        "source_url": "https://bse.com/corporate-announcements/reliance-board-meeting-2024",
        "metadata": {"company": "RELIANCE", "announcement_type": "board_meeting"}
    },
    {
        "content_text": "TATA CONSULTANCY SERVICES reports strong quarterly revenue growth of 12% YoY, driven by increased demand in digital transformation services and cloud migration projects.",
        "source_platform": "NSE",
        "source_url": "https://nse.com/corporate-announcements/tcs-q3-results-2024",
        "metadata": {"company": "TCS", "announcement_type": "financial_results"}
    },
    {
        "content_text": "RBI announces new monetary policy measures to maintain price stability and support economic growth. Repo rate maintained at current levels with focus on inflation targeting.",
        "source_platform": "News",
        "source_url": "https://rbi.org.in/monetary-policy-announcement-2024",
        "metadata": {"source": "RBI Official", "category": "policy"}
    },
    {
        "content_text": "Market analysis: Indian equity markets show resilience amid global uncertainties. Diversified portfolio approach recommended for long-term wealth creation.",
        "source_platform": "News",
        "source_url": "https://economic-times.com/market-analysis-jan-2024",
        "metadata": {"source": "Economic Times", "category": "analysis"}
    },
    {
        "content_text": "SEBI introduces new guidelines for mutual fund disclosures to enhance transparency and investor protection. Implementation timeline and compliance requirements detailed.",
        "source_platform": "News",
        "source_url": "https://sebi.gov.in/mutual-fund-guidelines-2024",
        "metadata": {"source": "SEBI Official", "category": "regulation"}
    }
]

async def send_to_sentinel(session: aiohttp.ClientSession, base_url: str, api_key: str, payload: Dict):
    """Send a single payload to the Sentinel ingestion API."""
    headers = {
        "X-API-KEY": api_key,
        "Content-Type": "application/json"
    }
    
    async with session.post(f"{base_url}/api/ingest", json=payload, headers=headers) as response:
        if response.status == 200:
            result = await response.json()
            print(f"✅ Queued: {payload['content_text'][:50]}...")
            return result
        else:
            error_text = await response.text()
            print(f"❌ Failed: {payload['content_text'][:50]}... - {response.status}: {error_text}")
            return None

async def main():
    """Main seeding function."""
    # Configuration
    base_url = input("Enter your Sentinel deployment URL (e.g., https://your-app.vercel.app): ").strip()
    api_key = input("Enter your SENTINEL_API_KEY: ").strip()
    
    if not base_url or not api_key:
        print("❌ Both URL and API key are required!")
        return
    
    print(f"\n🛡️ Seeding Sentinel Enterprise at {base_url}")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        # Send all sample threats
        tasks = [
            send_to_sentinel(session, base_url, api_key, threat)
            for threat in SAMPLE_THREATS
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful = sum(1 for r in results if r is not None and not isinstance(r, Exception))
        print(f"\n📊 Seeding Complete: {successful}/{len(SAMPLE_THREATS)} items queued successfully")
        print("\n⏳ Note: Items will appear in the dashboard after processing (5-10 seconds)")
        print(f"🌐 View your dashboard at: {base_url}")

if __name__ == "__main__":
    asyncio.run(main())
