#!/usr/bin/env python3
"""
Comprehensive System Test for Sentinel
Tests all components to ensure they work correctly.
"""

import asyncio
import aiohttp
import json
import sys
from pathlib import Path

# Add API to path
sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "api"))

async def test_api_endpoints():
    """Test all API endpoints."""
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Testing API endpoints...")
    
    async with aiohttp.ClientSession() as session:
        # Test health endpoint
        try:
            async with session.get(f"{base_url}/health") as response:
                if response.status == 200:
                    print("✅ Health endpoint working")
                else:
                    print(f"❌ Health endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Health endpoint error: {e}")
        
        # Test content analysis endpoint
        try:
            test_content = {
                "content_text": "🚨 GUARANTEED 500% returns in 24 hours! Secret crypto algorithm revealed!",
                "source_platform": "Twitter",
                "page_url": "https://twitter.com/test"
            }
            
            async with session.post(
                f"{base_url}/api/analyze-content",
                json=test_content,
                headers={"X-API-Key": "dev-api-key-12345"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('status') == 'success':
                        data = result['data']
                        print(f"✅ Content analysis working - Score: {data['sentinel_score']}")
                        print(f"   Risk Level: {data['risk_level']}")
                        print(f"   Fraud Probability: {data['fraud_probability']:.2f}")
                    else:
                        print(f"❌ Content analysis failed: {result}")
                else:
                    print(f"❌ Content analysis endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Content analysis error: {e}")
        
        # Test video analysis endpoint
        try:
            video_data = {
                "video_url": "https://example.com/test-video.mp4",
                "page_url": "https://youtube.com/test"
            }
            
            async with session.post(
                f"{base_url}/api/analyze-video",
                json=video_data,
                headers={"X-API-Key": "dev-api-key-12345"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('status') == 'success':
                        data = result['data']
                        print(f"✅ Video analysis working - Deepfake Probability: {data['deepfake_probability']:.2f}")
                    else:
                        print(f"❌ Video analysis failed: {result}")
                else:
                    print(f"❌ Video analysis endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Video analysis error: {e}")
        
        # Test threat feed endpoint
        try:
            async with session.get(f"{base_url}/api/feed") as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('status') == 'success':
                        data = result['data']
                        print(f"✅ Threat feed working - {len(data)} items")
                    else:
                        print(f"❌ Threat feed failed: {result}")
                else:
                    print(f"❌ Threat feed endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Threat feed error: {e}")

async def test_ai_components():
    """Test individual AI components."""
    print("\n🤖 Testing AI components...")
    
    try:
        from core.ai_engine import FraudClassificationEngine, MultilingualSentimentEngine, VectorEmbeddingEngine
        
        # Test fraud detection
        fraud_engine = FraudClassificationEngine()
        fraud_result = await fraud_engine.classify_fraud_risk(
            "URGENT! Guaranteed 1000% profit in crypto! Limited time offer!"
        )
        print(f"✅ Fraud Detection - Probability: {fraud_result['fraud_probability']:.2f}")
        print(f"   Patterns: {fraud_result['detected_patterns']}")
        
        # Test sentiment analysis
        sentiment_engine = MultilingualSentimentEngine()
        sentiment_result = await sentiment_engine.analyze_sentiment(
            "This investment opportunity looks amazing! Don't miss out!"
        )
        print(f"✅ Sentiment Analysis - {sentiment_result['sentiment']} ({sentiment_result['confidence']:.2f})")
        
        # Test embeddings
        embedding_engine = VectorEmbeddingEngine()
        await embedding_engine.initialize()
        embedding = await embedding_engine.generate_embedding("Test investment content")
        print(f"✅ Vector Embeddings - {len(embedding)} dimensions")
        
    except Exception as e:
        print(f"❌ AI component test failed: {e}")

async def test_data_scrapers():
    """Test data scraping components."""
    print("\n📡 Testing data scrapers...")
    
    try:
        from core.production_scrapers import ProductionNewsMonitor, ProductionSocialMediaMonitor
        
        # Test news scraping
        news_monitor = ProductionNewsMonitor()
        news_articles = await news_monitor.get_financial_news(limit=3)
        print(f"✅ News scraping - {len(news_articles)} articles collected")
        
        # Test Reddit scraping
        social_monitor = ProductionSocialMediaMonitor()
        reddit_posts = await social_monitor.scrape_reddit_financial_posts(limit=3)
        print(f"✅ Reddit scraping - {len(reddit_posts)} posts collected")
        
    except Exception as e:
        print(f"❌ Data scraper test failed: {e}")

async def run_comprehensive_test():
    """Run all system tests."""
    print("🛡️ SENTINEL SYSTEM VALIDATION")
    print("=" * 40)
    
    # Test AI components first
    await test_ai_components()
    
    # Test data scrapers
    await test_data_scrapers()
    
    # Test API endpoints (requires server to be running)
    print("\n🌐 Testing API endpoints...")
    print("💡 Make sure the API server is running (python scripts/start_dev.py)")
    
    try:
        await test_api_endpoints()
    except Exception as e:
        print(f"⚠️ API endpoint tests skipped: {e}")
        print("   Start the API server first to run these tests")
    
    print("\n🎯 System validation complete!")
    print("\nNext steps:")
    print("1. Start API server: python scripts/start_dev.py")
    print("2. Load browser extension from apps/browser-extension/")
    print("3. Open web dashboard: http://127.0.0.1:3000")
    print("4. Test on social media sites with extension active")

if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())
