"""
Unit tests for RuleBasedTextAnalyzer
"""

import pytest
from ai_models.rule_based_text_analyzer import RuleBasedTextAnalyzer


@pytest.fixture
async def analyzer():
    """Create and initialize analyzer"""
    analyzer = RuleBasedTextAnalyzer()
    await analyzer.load_rules()
    return analyzer


@pytest.mark.asyncio
async def test_analyzer_initialization(analyzer):
    """Test analyzer initializes correctly"""
    assert analyzer.is_loaded
    assert analyzer.analyzer_version == "1.0.0-rules"
    assert len(analyzer.fraud_rules) > 0


@pytest.mark.asyncio
async def test_guaranteed_returns_detection(analyzer):
    """Test detection of guaranteed returns fraud pattern"""
    test_cases = [
        ("Guaranteed 500% returns!", True),
        ("Risk-free investment opportunity", True),
        ("100% guaranteed profit", True),
        ("This is a normal investment", False),
        ("Potential returns may vary", False)
    ]
    
    for text, should_detect in test_cases:
        result = await analyzer.analyze_text(text)
        
        if should_detect:
            assert result.fraud_probability > 0.3, f"Should detect fraud in: {text}"
            assert "guaranteed_returns" in [rule for rule in result.rule_matches.get("pattern_analysis", {}).get("detected_rules", [])]
        else:
            assert result.fraud_probability < 0.3, f"Should not detect fraud in: {text}"


@pytest.mark.asyncio
async def test_urgency_pressure_detection(analyzer):
    """Test detection of urgency pressure tactics"""
    test_cases = [
        ("Act now! Limited time offer!", True),
        ("Urgent - expires tonight!", True),
        ("Last chance to invest!", True),
        ("Take your time to decide", False),
        ("No rush, think it over", False)
    ]
    
    for text, should_detect in test_cases:
        result = await analyzer.analyze_text(text)
        
        if should_detect:
            assert "urgency_pressure" in result.rule_matches.get("pattern_analysis", {}).get("detected_rules", [])
        else:
            detected_rules = result.rule_matches.get("pattern_analysis", {}).get("detected_rules", [])
            assert "urgency_pressure" not in detected_rules


@pytest.mark.asyncio
async def test_unrealistic_claims_detection(analyzer):
    """Test detection of unrealistic return claims"""
    test_cases = [
        ("1000% returns overnight!", True),
        ("Get rich quick scheme", True),
        ("Instant millionaire", True),
        ("Reasonable 8% annual return", False),
        ("Conservative investment approach", False)
    ]
    
    for text, should_detect in test_cases:
        result = await analyzer.analyze_text(text)
        
        if should_detect:
            assert result.fraud_probability > 0.4, f"Should detect unrealistic claims in: {text}"


@pytest.mark.asyncio
async def test_threat_level_calculation(analyzer):
    """Test threat level calculation"""
    test_cases = [
        ("Guaranteed 1000% returns! Act now! Secret insider info!", "critical"),
        ("Limited time investment opportunity", "medium"),
        ("This is a legitimate investment proposal", "minimal")
    ]
    
    for text, expected_level in test_cases:
        result = await analyzer.analyze_text(text)
        assert result.threat_level == expected_level, f"Expected {expected_level} for: {text}"


@pytest.mark.asyncio
async def test_multiple_rule_triggers(analyzer):
    """Test that multiple rules can be triggered simultaneously"""
    text = "URGENT! Guaranteed 500% returns! Secret insider information! Act now before it's too late!"
    
    result = await analyzer.analyze_text(text)
    
    # Should trigger multiple rules
    detected_rules = result.rule_matches.get("pattern_analysis", {}).get("detected_rules", [])
    assert len(detected_rules) >= 3, "Should trigger multiple fraud rules"
    
    # Should have high fraud probability
    assert result.fraud_probability > 0.7, "Should have high fraud probability"
    assert result.threat_level in ["critical", "high"], "Should be high threat level"


@pytest.mark.asyncio
async def test_empty_text_handling(analyzer):
    """Test handling of empty or whitespace-only text"""
    with pytest.raises(Exception):  # Should raise an error for empty text
        await analyzer.analyze_text("")
    
    with pytest.raises(Exception):  # Should raise an error for whitespace-only text
        await analyzer.analyze_text("   \n\t   ")


@pytest.mark.asyncio
async def test_context_analysis(analyzer):
    """Test context analysis functionality"""
    text = "Great investment opportunity!"
    
    # Test with different contexts
    result_twitter = await analyzer.analyze_text(text, {"platform": "twitter"})
    result_linkedin = await analyzer.analyze_text(text, {"platform": "linkedin"})
    
    # Both should complete successfully
    assert result_twitter.analysis_id != result_linkedin.analysis_id
    assert result_twitter.processing_time_ms > 0
    assert result_linkedin.processing_time_ms > 0


@pytest.mark.asyncio
async def test_performance_timing(analyzer):
    """Test that analysis completes within reasonable time"""
    text = "This is a test message for performance timing."
    
    result = await analyzer.analyze_text(text)
    
    # Should complete within 1 second (1000ms)
    assert result.processing_time_ms < 1000, "Analysis should complete quickly"
    assert result.processing_time_ms > 0, "Processing time should be recorded"


@pytest.mark.asyncio
async def test_confidence_scoring(analyzer):
    """Test confidence scoring logic"""
    test_cases = [
        ("Guaranteed returns with no risk!", 0.8),  # High confidence
        ("Maybe some returns possible", 0.6),       # Medium confidence
        ("Normal business proposal", 0.6)           # Base confidence
    ]
    
    for text, min_confidence in test_cases:
        result = await analyzer.analyze_text(text)
        assert result.confidence >= min_confidence, f"Confidence too low for: {text}"
        assert result.confidence <= 1.0, "Confidence should not exceed 1.0"
