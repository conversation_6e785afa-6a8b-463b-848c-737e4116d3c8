#!/usr/bin/env python3
"""
Sentinel AI Backend - Windows Startup Script
Starts the real AI backend with Windows-compatible dependencies
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_windows_dependencies():
    """Check if Windows-compatible dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn', 
        'torch',
        'transformers',
        'timm',
        'opencv-python',
        'mediapipe'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"❌ {package} is missing")
    
    if missing_packages:
        logger.error(f"Missing packages: {', '.join(missing_packages)}")
        logger.info("Install with: pip install -r requirements-windows.txt")
        return False
    
    return True

def install_windows_dependencies():
    """Install Windows-compatible dependencies"""
    logger.info("📦 Installing Windows-compatible dependencies...")
    
    try:
        cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements-windows.txt"]
        logger.info(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("✅ Dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install dependencies: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def create_data_directories():
    """Create necessary data directories"""
    directories = ["data", "weight", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"📁 Created directory: {directory}")

def start_backend():
    """Start the AI backend server"""
    logger.info("🚀 Starting Sentinel AI Backend (Windows Mode)...")
    
    try:
        # Start the FastAPI server
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "127.0.0.1",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
        
        logger.info(f"Running command: {' '.join(cmd)}")
        logger.info("🌐 Server will be available at: http://127.0.0.1:8000")
        logger.info("📖 API docs will be available at: http://127.0.0.1:8000/docs")
        logger.info("Press Ctrl+C to stop the server")
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Server failed to start: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    logger.info("🛡️ Sentinel AI Backend - Windows Startup")
    logger.info("=" * 60)
    
    # Check if we're on Windows
    if os.name != 'nt':
        logger.warning("⚠️ This script is optimized for Windows. Use start_ai_backend.py for other platforms.")
    
    # Check dependencies
    logger.info("🔍 Checking Windows-compatible dependencies...")
    if not check_windows_dependencies():
        logger.info("📦 Installing missing dependencies...")
        if not install_windows_dependencies():
            logger.error("❌ Failed to install dependencies")
            sys.exit(1)
    
    # Create directories
    logger.info("📁 Creating directories...")
    create_data_directories()
    
    # Note about optional features
    logger.info("ℹ️ Note: Some features may be limited on Windows:")
    logger.info("  - Face recognition (dlib) is disabled")
    logger.info("  - Audio analysis (librosa) may be limited")
    logger.info("  - Video processing uses basic OpenCV")
    logger.info("  - GenConViT models will use random weights (demo mode)")
    
    # Start backend
    logger.info("🚀 Starting AI backend...")
    start_backend()

if __name__ == "__main__":
    main()
