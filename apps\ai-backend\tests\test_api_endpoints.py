"""
Integration tests for API endpoints
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock


def test_health_check(client):
    """Test health check endpoint"""
    response = client.get("/api/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] in ["healthy", "degraded", "unhealthy"]
    assert "components" in data
    assert "version" in data


def test_health_check_rate_limiting(client):
    """Test health check rate limiting"""
    # Make multiple requests quickly
    responses = []
    for _ in range(10):
        response = client.get("/api/health")
        responses.append(response.status_code)
    
    # Most should succeed, but rate limiting might kick in
    success_count = sum(1 for status in responses if status == 200)
    assert success_count >= 5, "At least some health checks should succeed"


@patch('main.text_analyzer')
@patch('main.sentiment_model')
@patch('main.db_manager')
def test_analyze_text_success(mock_db, mock_sentiment, mock_analyzer, client, sample_text_requests):
    """Test successful text analysis"""
    # Mock the analyzer and sentiment model
    mock_analyzer.is_loaded = True
    mock_analyzer.analyzer_version = "1.0.0-rules"
    
    # Mock analysis result
    mock_result = AsyncMock()
    mock_result.analysis_id = "test-id-123"
    mock_result.fraud_probability = 0.85
    mock_result.confidence = 0.92
    mock_result.threat_level = "critical"
    mock_result.risk_indicators = ["Test indicator"]
    mock_result.processing_time_ms = 45.2
    mock_result.rule_matches = {"pattern_analysis": {"detected_rules": ["test_rule"]}}
    
    mock_analyzer.analyze_text.return_value = mock_result
    
    # Mock sentiment result
    mock_sentiment_result = AsyncMock()
    mock_sentiment_result.sentiment_score = -0.2
    mock_sentiment.is_loaded = True
    mock_sentiment.analyze_sentiment.return_value = mock_sentiment_result
    
    # Mock database operations
    mock_db.store_analysis_request.return_value = "request-id-123"
    mock_db.store_analysis_result.return_value = "result-id-123"
    
    # Test the endpoint
    test_request = sample_text_requests[0]
    response = client.post("/api/analyze-text", json=test_request)
    
    assert response.status_code == 200
    data = response.json()
    
    assert "analysis_id" in data
    assert "fraud_probability" in data
    assert "confidence" in data
    assert "threat_level" in data
    assert "risk_indicators" in data
    assert "sentiment_score" in data
    assert "processing_time_ms" in data
    assert "model_version" in data


def test_analyze_text_invalid_input(client):
    """Test text analysis with invalid input"""
    # Empty text
    response = client.post("/api/analyze-text", json={"text": ""})
    assert response.status_code == 422  # Validation error
    
    # Missing text field
    response = client.post("/api/analyze-text", json={"url": "https://example.com"})
    assert response.status_code == 422
    
    # Text too long
    long_text = "x" * 20000  # Exceeds max length
    response = client.post("/api/analyze-text", json={"text": long_text})
    assert response.status_code == 422


def test_analyze_text_invalid_url(client):
    """Test text analysis with invalid URL"""
    test_cases = [
        {"text": "Test", "url": "not-a-url"},
        {"text": "Test", "url": "ftp://example.com"},
        {"text": "Test", "url": "javascript:alert('xss')"},
        {"text": "Test", "url": "file:///etc/passwd"}
    ]
    
    for test_case in test_cases:
        response = client.post("/api/analyze-text", json=test_case)
        # Should either be validation error (422) or bad request (400)
        assert response.status_code in [400, 422]


@patch('main.db_manager')
def test_feedback_submission(mock_db, client):
    """Test feedback submission"""
    mock_db.store_user_feedback.return_value = "feedback-id-123"
    
    feedback_data = {
        "analysis_id": "123e4567-e89b-12d3-a456-426614174000",
        "is_correct": True,
        "user_comment": "The analysis was accurate"
    }
    
    response = client.post("/api/feedback", json=feedback_data)
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "success"
    assert "feedback_id" in data
    assert "message" in data


def test_feedback_invalid_analysis_id(client):
    """Test feedback with invalid analysis ID"""
    feedback_data = {
        "analysis_id": "invalid-id",
        "is_correct": True
    }
    
    response = client.post("/api/feedback", json=feedback_data)
    assert response.status_code == 400  # Bad request due to invalid UUID format


def test_stats_endpoint_requires_auth(client):
    """Test that stats endpoint requires authentication"""
    response = client.get("/api/stats")
    # Should require authentication when REQUIRE_API_KEY is True
    # In test environment, it might be disabled, so check for either success or auth error
    assert response.status_code in [200, 401]


def test_cors_headers(client):
    """Test CORS headers are present"""
    response = client.options("/api/health")
    # FastAPI automatically handles OPTIONS requests for CORS
    assert response.status_code in [200, 405]  # 405 if OPTIONS not explicitly handled


def test_security_headers(client):
    """Test security headers are present"""
    response = client.get("/api/health")
    
    # Check for security headers (these are added by our middleware)
    headers = response.headers
    
    # Note: TestClient might not include all middleware headers
    # In a real test, you'd check for:
    # - X-Content-Type-Options
    # - X-Frame-Options
    # - X-XSS-Protection
    # - Referrer-Policy
    
    assert response.status_code == 200


def test_api_error_handling(client):
    """Test API error handling"""
    # Test non-existent endpoint
    response = client.get("/api/nonexistent")
    assert response.status_code == 404
    
    # Test invalid method
    response = client.patch("/api/health")
    assert response.status_code == 405


@patch('main.text_analyzer', None)
def test_service_unavailable(client):
    """Test service unavailable when analyzer is not loaded"""
    response = client.post("/api/analyze-text", json={"text": "test"})
    assert response.status_code == 503  # Service unavailable


def test_request_size_limits(client):
    """Test request size limits"""
    # Create a very large request
    large_data = {
        "text": "x" * 1000,
        "context": {"large_field": "y" * 10000}
    }
    
    response = client.post("/api/analyze-text", json=large_data)
    # Should either succeed or fail with appropriate error
    assert response.status_code in [200, 400, 413, 422]
