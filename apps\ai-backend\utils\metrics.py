"""
Metrics collection for AI model performance
"""

import asyncio
import logging
import time
from collections import defaultdict, deque
from typing import Dict, List
import json

logger = logging.getLogger(__name__)


class MetricsCollector:
    """Collect and track AI model performance metrics"""
    
    def __init__(self):
        self.analysis_counts = defaultdict(int)
        self.threat_level_counts = defaultdict(int)
        self.processing_times = defaultdict(deque)
        self.accuracy_scores = deque(maxlen=1000)
        self.start_time = time.time()
        
        logger.info("📊 MetricsCollector initialized")
    
    def record_analysis(self, analysis_type: str, threat_level: str, processing_time_ms: float = 0):
        """Record an analysis event"""
        self.analysis_counts[analysis_type] += 1
        self.threat_level_counts[threat_level] += 1
        
        if processing_time_ms > 0:
            self.processing_times[analysis_type].append(processing_time_ms)
            # Keep only last 100 processing times
            if len(self.processing_times[analysis_type]) > 100:
                self.processing_times[analysis_type].popleft()
    
    def record_accuracy(self, accuracy: float):
        """Record model accuracy score"""
        self.accuracy_scores.append(accuracy)
    
    async def get_summary(self) -> Dict:
        """Get metrics summary"""
        uptime_seconds = time.time() - self.start_time
        
        # Calculate average processing times
        avg_processing_times = {}
        for analysis_type, times in self.processing_times.items():
            if times:
                avg_processing_times[analysis_type] = sum(times) / len(times)
        
        # Calculate accuracy
        avg_accuracy = sum(self.accuracy_scores) / len(self.accuracy_scores) if self.accuracy_scores else 0.0
        
        return {
            "uptime_seconds": uptime_seconds,
            "total_analyses": sum(self.analysis_counts.values()),
            "analysis_breakdown": dict(self.analysis_counts),
            "threat_level_breakdown": dict(self.threat_level_counts),
            "average_processing_times_ms": avg_processing_times,
            "average_accuracy": avg_accuracy,
            "recent_accuracy_samples": len(self.accuracy_scores)
        }
