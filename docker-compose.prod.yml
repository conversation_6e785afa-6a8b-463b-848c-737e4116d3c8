# Sentinel V1 Production Environment
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sentinel-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sentinel}
      POSTGRES_USER: ${POSTGRES_USER:-sentinel}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-sentinel}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: sentinel-redis-prod
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Sentinel Backend API
  api:
    build:
      context: ./apps/ai-backend
      dockerfile: Dockerfile
    container_name: sentinel-api-prod
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-sentinel}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-sentinel}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - CORS_ORIGINS=${CORS_ORIGINS:-https://yourdomain.com}
      - API_SECRET_KEY=${API_SECRET_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=false
      - REQUIRE_API_KEY=true
      - RATE_LIMIT_PER_MINUTE=${RATE_LIMIT_PER_MINUTE:-60}
      - RATE_LIMIT_PER_HOUR=${RATE_LIMIT_PER_HOUR:-1000}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/api/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: sentinel-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: sentinel-prod-network
