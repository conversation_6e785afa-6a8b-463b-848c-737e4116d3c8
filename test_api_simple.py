#!/usr/bin/env python3
"""Simple API test to validate the live system."""

import requests
import json

def main():
    print("🛡️ TESTING LIVE SENTINEL API")
    print("=" * 40)
    
    api_url = "http://127.0.0.1:8000"
    
    # Test health endpoint
    try:
        response = requests.get(f"{api_url}/api/feed", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ API Server: RUNNING")
            print(f"✅ Threat Feed: {len(data.get('data', []))} items")
        else:
            print(f"❌ API Error: {response.status_code}")
    except Exception as e:
        print(f"❌ Connection Failed: {e}")
        print("🔧 Start server: python services/api/start_sentinel.py")
        return
    
    # Test fraud analysis
    print("\n🧪 Testing Fraud Analysis")
    print("-" * 40)
    
    test_content = "GUARANTEED 500% returns in 24 hours! Secret algorithm!"
    
    payload = {
        "content": test_content
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(
            f"{api_url}/api/analyze",
            headers=headers,
            json=payload,
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ FRAUD ANALYSIS WORKING!")
        else:
            print("❌ Analysis failed")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
    
    print("\n🎯 SYSTEM READY FOR DEMO!")

if __name__ == "__main__":
    main()
