# 🎬 Sentinel Enterprise - Demo Setup Guide

## 🚀 Quick Start (2 minutes)

### 1. Start the Backend API
```bash
cd services/api
python -m uvicorn api.index:app --reload --port 8000
```
**Expected**: Server starts on http://127.0.0.1:8000

### 2. Start the Frontend Dashboard
```bash
cd apps/web
npm run dev
```
**Expected**: Dashboard opens on http://localhost:3001

### 3. Load Browser Extension
1. Open Chrome → Extensions → Developer mode
2. Click "Load unpacked"
3. Select `apps/browser-extension` folder
4. Extension icon appears in toolbar

---

## 🎯 Demo Features Ready

### ✅ **Enhanced Dashboard**
- **Real-time threat feed** with 8 realistic scenarios
- **Live content analysis** with instant scoring
- **Professional animations** and visual polish
- **Interactive filtering** by threat level
- **Real-time notifications** for new threats

### ✅ **Realistic Threat Data**
1. **CEO Deepfake Video** (Score: 15/100 - CRITICAL)
2. **Insider Trading Claims** (Score: 35/100 - HIGH)  
3. **Fed Misinformation** (Score: 28/100 - HIGH)
4. **Phishing Attack** (Score: 8/100 - CRITICAL)
5. **Fake Trading Guru** (Score: 22/100 - HIGH)
6. **Legitimate Content** (Score: 78/100 - LOW)
7. **Crypto Pump Scheme** (Score: 12/100 - CRITICAL)
8. **Celebrity Deepfake** (Score: 18/100 - HIGH)

### ✅ **Interactive Analysis**
Test these examples in the "Live Content Analysis" section:

**High Risk:**
```
🚨 URGENT: Guaranteed 500% returns in 24 hours! Secret crypto algorithm revealed. Limited spots available!
```

**Medium Risk:**
```
Investment opportunity in emerging markets. Potential returns of 15-20% annually. Consult your advisor.
```

**Low Risk:**
```
Market analysis shows steady growth in tech sector. Diversified portfolio recommended for long-term investors.
```

### ✅ **Browser Extension**
- **Enhanced visual design** with gradient badges
- **Real-time threat overlays** on social media
- **Critical threat animations** for high-risk content
- **Professional tooltips** with detailed analysis

---

## 🎬 Demo Flow (5-7 minutes)

1. **Open Dashboard** → Show live threat monitoring
2. **Demonstrate Analysis** → Use preset examples
3. **Show Browser Extension** → Visit social media sites
4. **Highlight Enterprise Features** → API docs, security
5. **Showcase Real-time Updates** → Live feed refresh

---

## 🔧 Troubleshooting

**API Server Issues:**
```bash
cd services/api
python -c "import fastapi; print('Dependencies OK')"
```

**Frontend Issues:**
```bash
cd apps/web
npm install
npm run dev
```

**Extension Issues:**
- Ensure Developer mode is enabled
- Check console for errors
- Reload extension if needed

---

## 🎯 Demo Success Checklist

- ✅ API server running on port 8000
- ✅ Frontend dashboard on port 3001
- ✅ Browser extension loaded and active
- ✅ Real-time threat feed displaying
- ✅ Content analysis working instantly
- ✅ Professional animations and polish
- ✅ Realistic threat scenarios loaded

**Your Sentinel Enterprise demo is production-ready! 🚀**
