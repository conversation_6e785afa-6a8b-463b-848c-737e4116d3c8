{"builds": [{"src": "apps/web/package.json", "use": "@vercel/next"}, {"src": "services/api/api/index.py", "use": "@vercel/python"}, {"src": "services/api/api/process-queue.py", "use": "@vercel/python"}, {"src": "services/api/api/extension.py", "use": "@vercel/python"}, {"src": "services/api/api/cron-ingest.py", "use": "@vercel/python"}], "routes": [{"src": "/api/process-queue", "dest": "services/api/api/process-queue.py"}, {"src": "/api/analyze-content", "dest": "services/api/api/extension.py"}, {"src": "/api/analyze-video", "dest": "services/api/api/extension.py"}, {"src": "/api/extension/(.*)", "dest": "services/api/api/extension.py"}, {"src": "/api/cron/(.*)", "dest": "services/api/api/cron-ingest.py"}, {"src": "/api/(.*)", "dest": "services/api/api/index.py"}, {"src": "/(.*)", "dest": "apps/web/$1"}], "env": {"PYTHONPATH": "services/api"}, "crons": [{"path": "/api/cron/ingest-data", "schedule": "*/15 * * * *"}]}