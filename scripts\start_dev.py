#!/usr/bin/env python3
"""
Development Startup Script for Sentinel
Initializes database, starts API server, and validates all systems.
"""

import asyncio
import subprocess
import sys
import os
import time
from pathlib import Path

# Add the services/api directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "api"))

async def check_dependencies():
    """Check if all required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'asyncpg', 'transformers', 
        'torch', 'opencv-python', 'sentence-transformers'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing.append(package)
            print(f"❌ {package} - MISSING")
    
    if missing:
        print(f"\n🚨 Missing packages: {', '.join(missing)}")
        print("Run: pip install -r services/api/requirements.txt")
        return False
    
    print("✅ All dependencies satisfied")
    return True

async def initialize_database():
    """Initialize the database schema."""
    print("\n🗄️ Initializing database...")
    
    try:
        from scripts.init_db import create_database_schema
        await create_database_schema()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        print("💡 Make sure PostgreSQL is running and connection string is correct")
        return False

async def test_ai_models():
    """Test AI model initialization."""
    print("\n🤖 Testing AI models...")
    
    try:
        from core.ai_engine import EnterpriseAIOrchestrator
        
        orchestrator = EnterpriseAIOrchestrator()
        
        # Test fraud detection
        print("  Testing fraud detection...")
        fraud_result = await orchestrator.fraud_engine.classify_fraud_risk(
            "Guaranteed 500% returns in 24 hours! Secret algorithm!"
        )
        print(f"  ✅ Fraud detection: {fraud_result['fraud_probability']:.2f} probability")
        
        # Test sentiment analysis
        print("  Testing sentiment analysis...")
        sentiment_result = await orchestrator.sentiment_engine.analyze_sentiment(
            "This is an amazing investment opportunity!"
        )
        print(f"  ✅ Sentiment analysis: {sentiment_result['sentiment']} ({sentiment_result['confidence']:.2f})")
        
        # Test embeddings
        print("  Testing embeddings...")
        embedding = await orchestrator.embedding_engine.generate_embedding("Test content")
        print(f"  ✅ Embeddings: {len(embedding)} dimensions")
        
        print("✅ All AI models working")
        return True
        
    except Exception as e:
        print(f"❌ AI model test failed: {e}")
        return False

async def test_deepfake_detection():
    """Test deepfake detection system."""
    print("\n🎭 Testing deepfake detection...")
    
    try:
        from core.working_deepfake_detection import ProductionDeepfakeDetector
        
        detector = ProductionDeepfakeDetector()
        await detector.initialize()
        
        # Test URL analysis (heuristic)
        test_url = "https://example.com/suspicious-deepfake-video.mp4"
        result = detector._analyze_url_patterns(test_url)
        
        print(f"  ✅ URL analysis: {result['probability']:.2f} probability")
        print("✅ Deepfake detection system ready")
        return True
        
    except Exception as e:
        print(f"❌ Deepfake detection test failed: {e}")
        return False

def start_api_server():
    """Start the FastAPI development server."""
    print("\n🚀 Starting API server...")
    
    try:
        # Change to API directory
        api_dir = Path(__file__).parent.parent / "services" / "api"
        os.chdir(api_dir)
        
        # Start uvicorn server
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "api.index:app", 
            "--host", "127.0.0.1", 
            "--port", "8000", 
            "--reload",
            "--log-level", "info"
        ]
        
        print("🌐 API server starting at http://127.0.0.1:8000")
        print("📚 API docs available at http://127.0.0.1:8000/docs")
        print("🛡️ Extension API at http://127.0.0.1:8000/api/analyze-content")
        print("\nPress Ctrl+C to stop the server")
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")

async def main():
    """Main startup sequence."""
    print("🛡️ SENTINEL ENTERPRISE - DEVELOPMENT STARTUP")
    print("=" * 50)
    
    # Check dependencies
    if not await check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return
    
    # Initialize database
    if not await initialize_database():
        print("\n⚠️ Database initialization failed. API will run in fallback mode.")
    
    # Test AI models
    if not await test_ai_models():
        print("\n⚠️ AI model test failed. Some features may not work correctly.")
    
    # Test deepfake detection
    if not await test_deepfake_detection():
        print("\n⚠️ Deepfake detection test failed. Video analysis may not work.")
    
    print("\n🎯 System validation complete!")
    print("🚀 Ready to start API server...")
    
    # Wait a moment then start server
    time.sleep(2)
    start_api_server()

if __name__ == "__main__":
    asyncio.run(main())
