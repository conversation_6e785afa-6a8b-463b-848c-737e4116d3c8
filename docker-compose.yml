# Sentinel V1 Development Environment
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sentinel-postgres
    environment:
      POSTGRES_DB: sentinel
      POSTGRES_USER: sentinel
      POSTGRES_PASSWORD: sentinel_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sentinel"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: sentinel-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Sentinel Backend API
  api:
    build:
      context: ./apps/ai-backend
      dockerfile: Dockerfile
    container_name: sentinel-api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*********************************************************/sentinel
      - REDIS_URL=redis://redis:6379/0
      - CORS_ORIGINS=http://localhost:3000,chrome-extension://
      - API_SECRET_KEY=dev-secret-key-change-in-production
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./apps/ai-backend:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/api/health')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web Application (Next.js)
  web:
    build:
      context: ./apps/web
      dockerfile: Dockerfile
    container_name: sentinel-web
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
    depends_on:
      - api
    volumes:
      - ./apps/web:/app
      - /app/node_modules
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: sentinel-network
