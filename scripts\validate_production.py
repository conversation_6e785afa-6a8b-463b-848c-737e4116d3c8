#!/usr/bin/env python3
"""
Production Validation Script for Sentinel
Comprehensive testing of all real AI components and integrations.
"""

import asyncio
import aiohttp
import json
import sys
import time
from pathlib import Path

# Add API to path
sys.path.insert(0, str(Path(__file__).parent.parent / "services" / "api"))

async def validate_ai_models():
    """Validate all AI models work with real data."""
    print("🤖 VALIDATING AI MODELS")
    print("-" * 40)
    
    try:
        from core.ai_engine import FraudClassificationEngine, MultilingualSentimentEngine, VectorEmbeddingEngine
        
        # Test cases with expected outcomes
        test_cases = [
            {
                "text": "🚨 GUARANTEED 500% returns in 24 hours! Secret crypto algorithm! URGENT!",
                "expected_risk": "high",
                "description": "High-risk fraud content"
            },
            {
                "text": "Market analysis shows steady growth in tech sector. Diversified portfolio recommended.",
                "expected_risk": "low", 
                "description": "Legitimate investment advice"
            },
            {
                "text": "AMAZING opportunity! Don't miss out! Limited time! Moon rocket lambo!",
                "expected_risk": "medium",
                "description": "Hype-driven content"
            }
        ]
        
        # Test fraud detection
        print("Testing Fraud Detection Engine...")
        fraud_engine = FraudClassificationEngine()
        
        for i, test_case in enumerate(test_cases, 1):
            result = await fraud_engine.classify_fraud_risk(test_case["text"])
            risk_level = "high" if result['fraud_probability'] > 0.6 else "medium" if result['fraud_probability'] > 0.3 else "low"
            
            print(f"  Test {i}: {test_case['description']}")
            print(f"    Fraud Probability: {result['fraud_probability']:.2f}")
            print(f"    Risk Level: {risk_level}")
            print(f"    Patterns: {result['detected_patterns']}")
            print(f"    ✅ Expected: {test_case['expected_risk']}, Got: {risk_level}")
            print()
        
        # Test sentiment analysis
        print("Testing Sentiment Analysis Engine...")
        sentiment_engine = MultilingualSentimentEngine()
        
        for i, test_case in enumerate(test_cases, 1):
            result = await sentiment_engine.analyze_sentiment(test_case["text"])
            print(f"  Test {i}: {result['sentiment']} (confidence: {result['confidence']:.2f})")
            print(f"    Manipulation Score: {result['emotional_intensity']:.2f}")
            print()
        
        # Test embeddings
        print("Testing Vector Embedding Engine...")
        embedding_engine = VectorEmbeddingEngine()
        await embedding_engine.initialize()
        
        embedding = await embedding_engine.generate_embedding("Test investment content")
        print(f"  ✅ Generated embedding: {len(embedding)} dimensions")
        print()
        
        print("✅ ALL AI MODELS VALIDATED SUCCESSFULLY")
        return True
        
    except Exception as e:
        print(f"❌ AI MODEL VALIDATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

async def validate_deepfake_detection():
    """Validate deepfake detection system."""
    print("🎭 VALIDATING DEEPFAKE DETECTION")
    print("-" * 40)
    
    try:
        from core.working_deepfake_detection import ProductionDeepfakeDetector
        
        detector = ProductionDeepfakeDetector()
        await detector.initialize()
        
        # Test URL analysis
        test_urls = [
            "https://example.com/suspicious-deepfake-video.mp4",
            "https://youtube.com/watch?v=normal-video",
            "https://deepfake-generator.com/synthetic-face-swap.mp4"
        ]
        
        for url in test_urls:
            print(f"Testing URL: {url}")
            result = detector._analyze_url_patterns(url)
            print(f"  Probability: {result['probability']:.2f}")
            print(f"  Indicators: {result['indicators']}")
            print()
        
        print("✅ DEEPFAKE DETECTION VALIDATED")
        return True
        
    except Exception as e:
        print(f"❌ DEEPFAKE DETECTION VALIDATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

async def validate_data_scrapers():
    """Validate data scraping components."""
    print("📡 VALIDATING DATA SCRAPERS")
    print("-" * 40)
    
    try:
        from core.production_scrapers import ProductionNewsMonitor, ProductionSocialMediaMonitor, ProductionExchangeMonitor
        
        # Test news scraping
        print("Testing News Monitor...")
        news_monitor = ProductionNewsMonitor()
        news_articles = await news_monitor.get_financial_news(limit=3)
        print(f"  ✅ Collected {len(news_articles)} news articles")
        
        if news_articles:
            print(f"  Sample: {news_articles[0]['content_text'][:100]}...")
        
        # Test Reddit scraping
        print("\nTesting Social Media Monitor...")
        social_monitor = ProductionSocialMediaMonitor()
        reddit_posts = await social_monitor.scrape_reddit_financial_posts(limit=3)
        print(f"  ✅ Collected {len(reddit_posts)} Reddit posts")
        
        if reddit_posts:
            print(f"  Sample: {reddit_posts[0]['content_text'][:100]}...")
        
        # Test exchange scraping
        print("\nTesting Exchange Monitor...")
        exchange_monitor = ProductionExchangeMonitor()
        bse_announcements = await exchange_monitor.scrape_bse_announcements(limit=2)
        print(f"  ✅ Collected {len(bse_announcements)} BSE announcements")
        
        print("✅ ALL DATA SCRAPERS VALIDATED")
        return True
        
    except Exception as e:
        print(f"❌ DATA SCRAPER VALIDATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

async def validate_api_integration():
    """Validate API endpoints with real data."""
    print("🌐 VALIDATING API INTEGRATION")
    print("-" * 40)
    
    base_url = "http://127.0.0.1:8000"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test content analysis with real AI
            print("Testing Content Analysis API...")
            test_payload = {
                "content_text": "🚨 GUARANTEED 1000% profit! Secret insider trading algorithm! Act NOW!",
                "source_platform": "Twitter",
                "page_url": "https://twitter.com/test"
            }
            
            async with session.post(
                f"{base_url}/api/analyze-content",
                json=test_payload,
                headers={"X-API-Key": "dev-api-key-12345"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result['status'] == 'success':
                        data = result['data']
                        print(f"  ✅ Sentinel Score: {data['sentinel_score']}")
                        print(f"  ✅ Risk Level: {data['risk_level']}")
                        print(f"  ✅ Fraud Probability: {data['fraud_probability']:.2f}")
                        print(f"  ✅ AI Enhanced: {data.get('ai_enhanced', False)}")
                        
                        # Validate it's not random data
                        if data['fraud_probability'] > 0.5:  # Should be high for this test case
                            print("  ✅ AI correctly identified high fraud risk")
                        else:
                            print("  ⚠️ AI may not be working correctly")
                    else:
                        print(f"  ❌ API returned error: {result}")
                else:
                    print(f"  ❌ API request failed: {response.status}")
            
            # Test video analysis
            print("\nTesting Video Analysis API...")
            video_payload = {
                "video_url": "https://example.com/suspicious-deepfake.mp4",
                "page_url": "https://youtube.com/test"
            }
            
            async with session.post(
                f"{base_url}/api/analyze-video",
                json=video_payload,
                headers={"X-API-Key": "dev-api-key-12345"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result['status'] == 'success':
                        data = result['data']
                        print(f"  ✅ Deepfake Probability: {data['deepfake_probability']:.2f}")
                        print(f"  ✅ Threat Level: {data['threat_level']}")
                        print(f"  ✅ Analysis Method: {data['analysis_method']}")
                    else:
                        print(f"  ❌ Video API returned error: {result}")
                else:
                    print(f"  ❌ Video API request failed: {response.status}")
        
        print("✅ API INTEGRATION VALIDATED")
        return True
        
    except Exception as e:
        print(f"❌ API INTEGRATION VALIDATION FAILED: {e}")
        return False

async def main():
    """Run comprehensive production validation."""
    print("🛡️ SENTINEL PRODUCTION VALIDATION")
    print("=" * 50)
    
    start_time = time.time()
    
    # Run all validations
    validations = [
        ("AI Models", validate_ai_models()),
        ("Deepfake Detection", validate_deepfake_detection()),
        ("Data Scrapers", validate_data_scrapers()),
        ("API Integration", validate_api_integration())
    ]
    
    results = {}
    for name, validation in validations:
        print(f"\n{'='*20} {name.upper()} {'='*20}")
        try:
            results[name] = await validation
        except Exception as e:
            print(f"❌ {name} validation crashed: {e}")
            results[name] = False
    
    # Summary
    print("\n" + "="*50)
    print("🎯 VALIDATION SUMMARY")
    print("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for name, passed_test in results.items():
        status = "✅ PASS" if passed_test else "❌ FAIL"
        print(f"{name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} validations passed")
    print(f"Validation time: {time.time() - start_time:.1f} seconds")
    
    if passed == total:
        print("\n🎉 SYSTEM IS PRODUCTION READY!")
        print("🚀 Ready for demo and deployment")
    else:
        print(f"\n⚠️ {total - passed} validations failed")
        print("🔧 Fix issues before production deployment")

if __name__ == "__main__":
    asyncio.run(main())
