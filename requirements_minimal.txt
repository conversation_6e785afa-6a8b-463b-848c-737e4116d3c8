# Minimal Working Requirements for Sentinel
fastapi==0.109.0
uvicorn[standard]==0.27.0
pydantic==2.6.0
python-multipart==0.0.9
python-dotenv==1.0.0

# Database
asyncpg==0.29.0

# AI/ML - Minimal working set
transformers==4.35.0
torch==2.0.1
numpy==1.24.0
scikit-learn==1.3.0

# Computer Vision - Headless for serverless
opencv-python-headless==********
Pillow==10.0.0

# HTTP Clients
httpx==0.25.0
aiohttp==3.9.0

# Data Processing
pandas==2.1.0
feedparser==6.0.10

# Logging
structlog==24.1.0

# Scrapers
beautifulsoup4==4.12.2
requests==2.31.0
