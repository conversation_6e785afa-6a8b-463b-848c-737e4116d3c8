"""
Rule-Based Text Analyzer for Financial Fraud Detection
Uses pattern matching and linguistic analysis to identify potential fraud indicators
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Tuple
import re

logger = logging.getLogger(__name__)


class TextAnalysisResult:
    """Result of rule-based text analysis"""
    def __init__(
        self,
        analysis_id: str,
        fraud_probability: float,
        confidence: float,
        threat_level: str,
        risk_indicators: List[str],
        processing_time_ms: float,
        rule_matches: Dict
    ):
        self.analysis_id = analysis_id
        self.fraud_probability = fraud_probability
        self.confidence = confidence
        self.threat_level = threat_level
        self.risk_indicators = risk_indicators
        self.processing_time_ms = processing_time_ms
        self.rule_matches = rule_matches


class RuleBasedTextAnalyzer:
    """Rule-based text analyzer for financial fraud detection"""

    def __init__(self):
        self.analyzer_version = "1.0.0-rules"
        self.is_loaded = False

        # Rule patterns for fraud detection
        self.fraud_rules: Dict[str, Dict] = {}

        logger.info("🔍 RuleBasedTextAnalyzer initialized")

    async def load_rules(self):
        """Load fraud detection rules and patterns"""
        try:
            start_time = time.time()
            logger.info("📋 Loading fraud detection rules...")

            # Load fraud detection patterns
            await self._load_fraud_rules()

            self.is_loaded = True
            load_time = (time.time() - start_time) * 1000
            logger.info(f"✅ Fraud detection rules loaded in {load_time:.2f}ms")

        except Exception as e:
            logger.error(f"❌ Failed to load fraud detection rules: {e}")
            raise
    
    async def _load_fraud_rules(self):
        """Load fraud detection rules and patterns"""
        self.fraud_rules = {
            'guaranteed_returns': {
                'patterns': [
                    r'guaranteed?\s*\d+%', r'risk.?free', r'sure.?profit', r'certain.?returns',
                    r'guaranteed?\s*profit', r'no.?risk', r'safe.?investment', r'promise.?\d+%'
                ],
                'weight': 0.40,
                'severity': 'critical',
                'description': 'Promises guaranteed returns (impossible in real markets)'
            },
            'urgency_pressure': {
                'patterns': [
                    r'urgent', r'limited.?time', r'act.?now', r'expires?.?(soon|today|tonight)',
                    r'last.?chance', r'hurry', r'deadline', r'running.?out', r'only.?\d+.?left'
                ],
                'weight': 0.25,
                'severity': 'high',
                'description': 'Creates artificial urgency to prevent rational thinking'
            },
            'unrealistic_claims': {
                'patterns': [
                    r'\d{3,}%', r'millionaire.?overnight', r'instant.?wealth', r'get.?rich.?quick',
                    r'1000%', r'500%', r'200%', r'overnight.?success', r'instant.?millionaire'
                ],
                'weight': 0.45,
                'severity': 'critical',
                'description': 'Claims unrealistic returns that violate market fundamentals'
            },
            'pump_dump_indicators': {
                'patterns': [
                    r'pump', r'moon', r'rocket', r'lambo', r'diamond.?hands', r'hodl',
                    r'ape', r'to.?the.?moon', r'rocket.?ship'
                ],
                'weight': 0.35,
                'severity': 'high',
                'description': 'Language associated with pump and dump schemes'
            },
            'insider_trading_claims': {
                'patterns': [
                    r'insider', r'secret', r'exclusive', r'leaked', r'confidential',
                    r'private.?tip', r'inside.?information', r'exclusive.?access'
                ],
                'weight': 0.40,
                'severity': 'critical',
                'description': 'Claims of insider information (illegal market manipulation)'
            },
            'emotional_manipulation': {
                'patterns': [
                    r'don.?t.?miss', r'fomo', r'regret', r'jealous', r'everyone.?is.?making',
                    r'you.?ll.?regret', r'missing.?out', r'fear.?of.?missing'
                ],
                'weight': 0.20,
                'severity': 'medium',
                'description': 'Uses fear and social pressure to manipulate decisions'
            }
        }

        logger.info(f"Loaded {len(self.fraud_rules)} fraud detection rule categories")
    
    async def analyze_text(self, text: str, context: Optional[Dict] = None) -> TextAnalysisResult:
        """Analyze text using rule-based fraud detection"""
        if not self.is_loaded:
            raise RuntimeError("Rules not loaded")

        start_time = time.time()
        analysis_id = str(uuid.uuid4())

        try:
            # 1. Pattern-based rule analysis
            pattern_result = await self._analyze_with_rules(text)

            # 2. Linguistic analysis
            linguistic_result = await self._analyze_linguistics(text)

            # 3. Context analysis
            context_result = await self._analyze_context(text, context)

            # Combine all results using weighted scoring
            fraud_probability, confidence, risk_indicators = self._combine_rule_results(
                pattern_result,
                linguistic_result,
                context_result
            )

            # Determine threat level
            threat_level = self._determine_threat_level(fraud_probability, confidence)

            processing_time = (time.time() - start_time) * 1000

            rule_matches = {
                "pattern_analysis": pattern_result,
                "linguistic_analysis": linguistic_result,
                "context_analysis": context_result
            }

            logger.info(f"🔍 Rule-based analysis complete: {fraud_probability:.3f} probability, {confidence:.3f} confidence")

            return TextAnalysisResult(
                analysis_id=analysis_id,
                fraud_probability=fraud_probability,
                confidence=confidence,
                threat_level=threat_level,
                risk_indicators=risk_indicators,
                processing_time_ms=processing_time,
                rule_matches=rule_matches
            )

        except Exception as e:
            logger.error(f"❌ Rule-based analysis failed: {e}")
            raise
    
    async def _analyze_with_rules(self, text: str) -> Dict:
        """Analyze text using rule-based pattern matching"""
        try:
            text_lower = text.lower()
            detected_rules = []
            severity_breakdown = {'critical': 0, 'high': 0, 'medium': 0}
            total_risk_score = 0.0
            rule_details = {}

            for rule_name, rule_data in self.fraud_rules.items():
                matches = 0
                matched_patterns = []

                for pattern in rule_data['patterns']:
                    if re.search(pattern, text_lower):
                        matches += 1
                        matched_patterns.append(pattern)

                if matches > 0:
                    detected_rules.append(rule_name)
                    severity_breakdown[rule_data['severity']] += 1
                    # Diminishing returns for multiple matches of same rule
                    score_contribution = rule_data['weight'] * min(matches * 0.7, 1.5)
                    total_risk_score += score_contribution

                    rule_details[rule_name] = {
                        'matches': matches,
                        'patterns': matched_patterns,
                        'weight': rule_data['weight'],
                        'severity': rule_data['severity'],
                        'description': rule_data['description'],
                        'score_contribution': score_contribution
                    }

            # Calculate final fraud probability (cap at 0.95)
            fraud_probability = min(total_risk_score, 0.95)

            # Calculate confidence based on number and severity of matches
            confidence_base = 0.6
            confidence_boost = (len(detected_rules) * 0.05) + (severity_breakdown['critical'] * 0.1)
            confidence = min(confidence_base + confidence_boost, 0.95)

            return {
                "fraud_probability": fraud_probability,
                "confidence": confidence,
                "detected_rules": detected_rules,
                "severity_breakdown": severity_breakdown,
                "rule_details": rule_details,
                "total_risk_score": total_risk_score
            }

        except Exception as e:
            logger.error(f"Rule-based analysis failed: {e}")
            return {"fraud_probability": 0.1, "confidence": 0.3, "error": str(e)}
    
    # This method is now replaced by _analyze_with_rules above
    # Keeping this comment for reference during refactoring
    
    async def _analyze_linguistics(self, text: str) -> Dict:
        """Analyze linguistic features"""
        try:
            # Count various linguistic features
            exclamation_count = text.count('!')
            caps_ratio = sum(1 for c in text if c.isupper()) / len(text) if text else 0
            number_count = len(re.findall(r'\d+', text))
            
            # Urgency words
            urgency_words = ['now', 'today', 'immediately', 'hurry', 'quick', 'fast']
            urgency_count = sum(1 for word in urgency_words if word in text.lower())
            
            # Calculate linguistic fraud indicators
            linguistic_score = 0
            if exclamation_count > 2:
                linguistic_score += 15
            if caps_ratio > 0.1:
                linguistic_score += 20
            if number_count > 3:
                linguistic_score += 10
            if urgency_count > 1:
                linguistic_score += 25
            
            fraud_probability = min(linguistic_score / 70.0, 0.8)
            confidence = 0.7
            
            return {
                "fraud_probability": fraud_probability,
                "confidence": confidence,
                "features": {
                    "exclamation_count": exclamation_count,
                    "caps_ratio": caps_ratio,
                    "number_count": number_count,
                    "urgency_count": urgency_count
                }
            }
            
        except Exception as e:
            logger.error(f"Linguistic analysis failed: {e}")
            return {"fraud_probability": 0.1, "confidence": 0.3, "error": str(e)}
    
    async def _analyze_context(self, text: str, context: Optional[Dict]) -> Dict:
        """Analyze contextual information"""
        if not context:
            return {"fraud_probability": 0.0, "confidence": 0.5}
        
        context_score = 0
        
        # Check URL context
        url = context.get('url', '')
        if any(suspicious in url.lower() for suspicious in ['bit.ly', 'tinyurl', 'telegram']):
            context_score += 20
        
        # Check platform context
        platform = context.get('platform', '')
        if platform in ['telegram', 'discord']:
            context_score += 10
        
        fraud_probability = min(context_score / 50.0, 0.6)
        confidence = 0.6
        
        return {
            "fraud_probability": fraud_probability,
            "confidence": confidence,
            "context_score": context_score
        }
    
    def _combine_rule_results(self, pattern_result: Dict, linguistic_result: Dict, context_result: Dict) -> Tuple[float, float, List[str]]:
        """Combine results from different rule-based analyses"""
        # Weights for different analysis types
        pattern_weight = 0.6  # Pattern matching gets highest weight
        linguistic_weight = 0.3
        context_weight = 0.1

        # Extract probabilities and confidences
        pattern_prob = pattern_result.get('fraud_probability', 0.0)
        linguistic_prob = linguistic_result.get('fraud_probability', 0.0)
        context_prob = context_result.get('fraud_probability', 0.0)

        pattern_conf = pattern_result.get('confidence', 0.5)
        linguistic_conf = linguistic_result.get('confidence', 0.5)
        context_conf = context_result.get('confidence', 0.5)

        # Weighted combination
        final_fraud_prob = (
            pattern_prob * pattern_weight +
            linguistic_prob * linguistic_weight +
            context_prob * context_weight
        )

        final_confidence = (
            pattern_conf * pattern_weight +
            linguistic_conf * linguistic_weight +
            context_conf * context_weight
        )

        # Collect all risk indicators
        all_indicators = []

        # Add pattern-based indicators
        if 'detected_rules' in pattern_result:
            for rule in pattern_result['detected_rules']:
                rule_details = pattern_result.get('rule_details', {}).get(rule, {})
                description = rule_details.get('description', rule)
                all_indicators.append(f"Rule: {description}")

        # Add linguistic indicators
        if 'indicators' in linguistic_result:
            all_indicators.extend(linguistic_result['indicators'])

        # Ensure bounds
        final_fraud_prob = max(0.0, min(1.0, final_fraud_prob))
        final_confidence = max(0.0, min(1.0, final_confidence))

        return final_fraud_prob, final_confidence, all_indicators[:10]  # Limit indicators
    
    def _determine_threat_level(self, fraud_probability: float, confidence: float) -> str:
        """Determine threat level based on probability and confidence"""
        if fraud_probability >= 0.8 and confidence >= 0.7:
            return "critical"
        elif fraud_probability >= 0.6 and confidence >= 0.6:
            return "high"
        elif fraud_probability >= 0.4 and confidence >= 0.5:
            return "medium"
        elif fraud_probability >= 0.2:
            return "low"
        else:
            return "minimal"
