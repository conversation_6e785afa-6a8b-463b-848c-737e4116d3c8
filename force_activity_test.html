<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Sentinel Activity Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-post {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .high-risk {
            border-left: 4px solid #ef4444;
        }
        .medium-risk {
            border-left: 4px solid #f59e0b;
        }
        .low-risk {
            border-left: 4px solid #10b981;
        }
        .stats-display {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #1f2937;
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <div class="stats-display" id="stats-display">
        📊 Waiting for Sentinel...
    </div>

    <h1>🛡️ Force Sentinel Activity Test</h1>
    <p>This page will force the extension to show activity by creating high-risk content.</p>

    <div class="test-post high-risk">
        <h3>🚨 Critical Fraud Post 1</h3>
        <p>GUARANTEED 1000% PROFIT IN 24 HOURS! Secret Wall Street algorithm revealed! URGENT - only 50 spots left! Risk-free investment opportunity that will make you rich overnight!</p>
    </div>

    <div class="test-post high-risk">
        <h3>🚨 Critical Fraud Post 2</h3>
        <p>EXCLUSIVE insider information leaked! Warren Buffett's secret strategy revealed! Download confidential report before it's removed! Limited time access to guaranteed profits!</p>
    </div>

    <div class="test-post medium-risk">
        <h3>⚠️ Pump & Dump Post</h3>
        <p>🚀 MOON TIME! Diamond hands! This crypto is going to PUMP! Get in before it rockets to the moon! HODL! Apes together strong! Don't miss this opportunity!</p>
    </div>

    <div class="test-post medium-risk">
        <h3>⚠️ FOMO Manipulation</h3>
        <p>You'll REGRET missing this opportunity FOREVER! FOMO is real! Last chance to join exclusive investment club! Act NOW or lose everything! Don't let others get rich while you stay poor!</p>
    </div>

    <div class="test-post low-risk">
        <h3>✅ Legitimate Content</h3>
        <p>Market analysis suggests diversified portfolio allocation for long-term growth. Consider 60% equities, 30% bonds, 10% alternatives. Consult your financial advisor for personalized investment advice based on your risk tolerance.</p>
    </div>

    <div class="test-post high-risk">
        <h3>🚨 More High-Risk Content</h3>
        <p>SECRET ALGORITHM REVEALED! Make $10,000 per day with this GUARANTEED system! URGENT - only available for 24 hours! Risk-free money-making opportunity that banks don't want you to know!</p>
    </div>

    <button onclick="addMoreContent()" style="
        background: #ef4444; color: white; border: none; 
        padding: 12px 24px; border-radius: 6px; cursor: pointer; 
        font-size: 16px; margin: 20px 0;
    ">
        🚨 Add More High-Risk Content
    </button>

    <button onclick="checkStats()" style="
        background: #3b82f6; color: white; border: none; 
        padding: 12px 24px; border-radius: 6px; cursor: pointer; 
        font-size: 16px; margin: 20px 0;
    ">
        📊 Check Extension Stats
    </button>

    <script>
        let contentCounter = 0;

        function addMoreContent() {
            contentCounter++;
            const container = document.body;
            
            const newPost = document.createElement('div');
            newPost.className = 'test-post high-risk';
            newPost.innerHTML = `
                <h3>🚨 Dynamic High-Risk Content ${contentCounter}</h3>
                <p>GUARANTEED ${Math.floor(Math.random() * 1000 + 500)}% returns! Secret insider trading algorithm! URGENT opportunity expires in ${Math.floor(Math.random() * 24 + 1)} hours! Risk-free investment that will make you a millionaire!</p>
            `;
            
            container.appendChild(newPost);
            console.log(`Added dynamic content ${contentCounter}`);
            
            // Update stats display
            setTimeout(checkStats, 1000);
        }

        function checkStats() {
            const statsDisplay = document.getElementById('stats-display');
            
            if (window.sentinelInstance) {
                const stats = window.sentinelInstance.stats;
                statsDisplay.innerHTML = `
                    📊 Sentinel Stats:<br>
                    Content: ${stats.contentAnalyzed}<br>
                    Threats: ${stats.threatsBlocked}
                `;
                
                console.log('Current Sentinel stats:', stats);
            } else {
                statsDisplay.innerHTML = '❌ Sentinel not found';
                console.log('Sentinel instance not found');
            }
        }

        // Auto-check stats every 3 seconds
        setInterval(checkStats, 3000);
        
        // Initial check after 2 seconds
        setTimeout(checkStats, 2000);
        
        // Add content automatically every 5 seconds for testing
        setInterval(() => {
            if (contentCounter < 5) {
                addMoreContent();
            }
        }, 5000);
    </script>
</body>
</html>
