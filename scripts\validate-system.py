#!/usr/bin/env python3
"""
Enterprise System Validation Script
Validates all components of the Sentinel platform.
"""

import asyncio
import aiohttp
import sys
from typing import Dict, Any

async def validate_health_endpoint(base_url: str) -> bool:
    """Validate the health endpoint."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/api/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Health Check: {data['status']}")
                    print(f"   Database: {'✅' if data['dependencies']['database'] else '❌'}")
                    print(f"   Queue: {'✅' if data['dependencies']['queue'] else '❌'}")
                    return data['status'] == 'ok'
                else:
                    print(f"❌ Health Check Failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Health Check Error: {e}")
        return False

async def validate_ingestion_endpoint(base_url: str, api_key: str) -> bool:
    """Validate the ingestion endpoint with authentication."""
    try:
        headers = {"X-API-KEY": api_key, "Content-Type": "application/json"}
        payload = {
            "content_text": "Test validation message for system check",
            "source_platform": "Other"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{base_url}/api/ingest", json=payload, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Ingestion: {data['status']}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Ingestion Failed: {response.status} - {error_text}")
                    return False
    except Exception as e:
        print(f"❌ Ingestion Error: {e}")
        return False

async def validate_feed_endpoint(base_url: str) -> bool:
    """Validate the threat feed endpoint."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/api/feed") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Feed Endpoint: {len(data.get('data', {}).get('feed', []))} items")
                    return True
                else:
                    print(f"❌ Feed Failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Feed Error: {e}")
        return False

async def main():
    """Main validation function."""
    print("🛡️ Sentinel Enterprise System Validation")
    print("=" * 50)
    
    base_url = input("Enter your deployment URL: ").strip()
    api_key = input("Enter your API key: ").strip()
    
    if not base_url or not api_key:
        print("❌ Both URL and API key are required!")
        return
    
    print(f"\n🔍 Validating system at {base_url}")
    print("-" * 40)
    
    # Run all validations
    health_ok = await validate_health_endpoint(base_url)
    ingest_ok = await validate_ingestion_endpoint(base_url, api_key)
    feed_ok = await validate_feed_endpoint(base_url)
    
    print("\n📊 Validation Summary")
    print("-" * 20)
    print(f"Health Endpoint: {'✅' if health_ok else '❌'}")
    print(f"Ingestion API: {'✅' if ingest_ok else '❌'}")
    print(f"Feed API: {'✅' if feed_ok else '❌'}")
    
    if all([health_ok, ingest_ok, feed_ok]):
        print("\n🎉 SYSTEM VALIDATION SUCCESSFUL")
        print("The Sentinel platform is enterprise-ready!")
    else:
        print("\n⚠️ SYSTEM VALIDATION FAILED")
        print("Please check the configuration and try again.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
