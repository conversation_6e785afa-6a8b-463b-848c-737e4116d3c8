"""
Authentication and authorization for Sentinel Backend
"""

import hashlib
import secrets
import logging
from typing import Optional
from fastapi import HTTPException, Security, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from datetime import datetime

from config import get_settings
from database.database import DatabaseManager
from database.models import User

logger = logging.getLogger(__name__)

# Security scheme for API key authentication
security = HTTPBearer(auto_error=False)


def generate_api_key() -> str:
    """Generate a secure API key"""
    return secrets.token_urlsafe(32)


def hash_api_key(api_key: str) -> str:
    """Hash an API key for secure storage"""
    return hashlib.sha256(api_key.encode()).hexdigest()


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Security(security),
    db_manager: DatabaseManager = Depends()
) -> Optional[User]:
    """Get current user from API key"""
    settings = get_settings()
    
    # If API key authentication is disabled, return None (allow anonymous access)
    if not settings.require_api_key:
        return None
    
    # Check for API key in Authorization header
    if not credentials:
        # Check for default API key if configured
        if settings.default_api_key:
            api_key = settings.default_api_key
        else:
            raise HTTPException(
                status_code=401,
                detail="API key required. Include 'Authorization: Bearer <api_key>' header."
            )
    else:
        api_key = credentials.credentials
    
    # Validate API key format
    if not api_key or len(api_key) < 16:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key format"
        )
    
    # Look up user by API key
    try:
        user = await db_manager.get_user_by_api_key(api_key)
        if not user:
            logger.warning(f"Invalid API key attempted: {api_key[:8]}...")
            raise HTTPException(
                status_code=401,
                detail="Invalid API key"
            )
        
        # Update last used timestamp
        # Note: In a production system, you might want to batch these updates
        # to avoid database writes on every request
        
        logger.info(f"Authenticated user: {user.name or user.id}")
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Authentication service error"
        )


async def require_api_key(
    user: Optional[User] = Depends(get_current_user)
) -> User:
    """Require valid API key authentication"""
    settings = get_settings()
    
    if settings.require_api_key and not user:
        raise HTTPException(
            status_code=401,
            detail="Valid API key required"
        )
    
    return user


class APIKeyManager:
    """Manage API keys for users"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    async def create_api_key(self, name: str = None, email: str = None) -> tuple[str, str]:
        """Create a new API key and user"""
        api_key = generate_api_key()
        
        try:
            user_id = await self.db_manager.create_user(
                api_key=api_key,
                name=name,
                email=email
            )
            
            logger.info(f"Created new API key for user: {name or user_id}")
            return api_key, user_id
            
        except Exception as e:
            logger.error(f"Failed to create API key: {e}")
            raise
    
    async def revoke_api_key(self, api_key: str) -> bool:
        """Revoke an API key (deactivate user)"""
        try:
            user = await self.db_manager.get_user_by_api_key(api_key)
            if not user:
                return False
            
            # In a real implementation, you'd update the user's is_active status
            # For now, we'll just log it
            logger.info(f"API key revoked for user: {user.name or user.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to revoke API key: {e}")
            return False
    
    async def list_api_keys(self) -> list:
        """List all active API keys (admin function)"""
        # This would be implemented with proper admin authentication
        # For now, just return empty list
        return []


def validate_request_size(content_length: Optional[int] = None) -> None:
    """Validate request size to prevent DoS attacks"""
    if content_length and content_length > 1024 * 1024:  # 1MB limit
        raise HTTPException(
            status_code=413,
            detail="Request too large. Maximum size is 1MB."
        )


def validate_content_length(text: str) -> None:
    """Validate text content length"""
    settings = get_settings()
    
    if len(text) > settings.max_text_length:
        raise HTTPException(
            status_code=400,
            detail=f"Text too long. Maximum length is {settings.max_text_length} characters."
        )


def validate_url_format(url: str) -> None:
    """Validate URL format and length"""
    settings = get_settings()
    
    if len(url) > settings.max_url_length:
        raise HTTPException(
            status_code=400,
            detail=f"URL too long. Maximum length is {settings.max_url_length} characters."
        )
    
    # Basic URL validation
    if not url.startswith(('http://', 'https://')):
        raise HTTPException(
            status_code=400,
            detail="Invalid URL format. Must start with http:// or https://"
        )
