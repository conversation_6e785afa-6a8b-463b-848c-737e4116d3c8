import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import DashboardPage from '@/app/page';
import React from 'react';

// Mock SWR
vi.mock('swr', () => ({
  default: vi.fn(() => ({
    data: {
      feed: [
        { 
          id: 1, 
          content_text: 'Guaranteed profit!', 
          source_platform: 'Telegram', 
          timestamp: new Date().toISOString(), 
          analysis: { 
            sentinel_score: 15,
            risk_factors: ['High-Risk Language', 'Unrealistic Promises'],
            confidence: 0.95,
            fraud_risk: 0.9,
            sentiment: 'negative',
            analysis_timestamp: new Date().toISOString()
          } 
        },
        { 
          id: 2, 
          content_text: 'Official board meeting.', 
          source_platform: 'BSE', 
          timestamp: new Date().toISOString(), 
          analysis: { 
            sentinel_score: 95,
            risk_factors: [],
            confidence: 0.98,
            fraud_risk: 0.05,
            sentiment: 'neutral',
            analysis_timestamp: new Date().toISOString()
          } 
        },
      ]
    },
    error: undefined,
    isLoading: false,
  })),
}));

describe('DashboardPage', () => {
  it('renders the threat feed table with data', async () => {
    render(<DashboardPage />);
    
    // Check for title
    expect(screen.getByText('Sentinel Threat Feed')).toBeInTheDocument();
    
    // Check for table rows from mocked data
    await waitFor(() => {
        expect(screen.getByText('Guaranteed profit!')).toBeInTheDocument();
        expect(screen.getByText('Official board meeting.')).toBeInTheDocument();
    });

    // Check for score badges
    const lowScoreBadge = screen.getByText('15');
    const highScoreBadge = screen.getByText('95');
    expect(lowScoreBadge).toBeInTheDocument();
    expect(highScoreBadge).toBeInTheDocument();
    // Check for destructive variant on low score
    expect(lowScoreBadge.className).toContain('destructive');
  });
});
