<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }
    
    .header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .logo {
      font-size: 24px;
      margin-right: 8px;
    }
    
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #1f2937;
    }
    
    .status {
      display: flex;
      align-items: center;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 16px;
    }
    
    .status.active {
      background: #dcfce7;
      color: #166534;
    }
    
    .status.inactive {
      background: #fef2f2;
      color: #991b1b;
    }
    
    .stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 16px;
    }
    
    .stat {
      text-align: center;
      padding: 12px;
      background: #f9fafb;
      border-radius: 6px;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #1f2937;
    }
    
    .stat-label {
      font-size: 12px;
      color: #6b7280;
      margin-top: 4px;
    }
    
    .settings-btn {
      width: 100%;
      padding: 8px 16px;
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .settings-btn:hover {
      background: #2563eb;
    }
  </style>
</head>
<body>
  <div class="header">
    <span class="logo">🛡️</span>
    <div class="title">Sentinel</div>
  </div>

  <div id="status" class="status active">
    <span style="margin-right: 8px;">●</span>
    <span>Active - Monitoring for threats</span>
  </div>

  <div class="stats">
    <div class="stat">
      <div id="threats-blocked" class="stat-value">0</div>
      <div class="stat-label">Threats Blocked</div>
    </div>
    <div class="stat">
      <div id="content-analyzed" class="stat-value">0</div>
      <div class="stat-label">Content Analyzed</div>
    </div>
  </div>

  <!-- <button class="settings-btn" id="settings-btn">
    Extension Settings
  </button> -->

  <script src="popup.js"></script>
</body>
</html>
