"""
Database connection and session management for Sentinel Backend
"""

import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import <PERSON><PERSON>Pool

from .models import Base, AnalysisRequest, AnalysisResult, User, UserFeedback
from .models import AnalysisRequestCreate, AnalysisResultCreate, UserFeedbackCreate

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database connection and session manager"""

    def __init__(self, database_url: str):
        self.database_url = database_url
        self.engine = None
        self.async_session_maker = None
        self.is_initialized = False

    async def initialize(self):
        """Initialize database connection and create tables"""
        try:
            logger.info("🗄️ Initializing database connection...")

            # Create async engine
            self.engine = create_async_engine(
                self.database_url,
                echo=False,  # Set to True for SQL debugging
                pool_pre_ping=True,
                pool_recycle=300,
            )

            # Create session maker
            self.async_session_maker = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

            # Create tables
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)

            self.is_initialized = True
            logger.info("✅ Database initialized successfully")

        except Exception as e:
            logger.error(f"❌ Failed to initialize database: {e}")
            raise

    async def close(self):
        """Close database connections"""
        if self.engine:
            await self.engine.dispose()
            logger.info("🔄 Database connections closed")

    async def is_healthy(self) -> bool:
        """Check if database is healthy"""
        if not self.is_initialized or not self.engine:
            return False

        try:
            async with self.engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False

    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session context manager"""
        if not self.is_initialized:
            raise RuntimeError("Database not initialized")

        async with self.async_session_maker() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    async def store_analysis_request(self, request_data: AnalysisRequestCreate) -> str:
        """Store analysis request and return ID"""
        async with self.get_session() as session:
            db_request = AnalysisRequest(
                source_type=request_data.source_type,
                content_hash=request_data.content_hash,
                url=request_data.url,
                platform=request_data.platform,
                context_data=request_data.context_data
            )
            session.add(db_request)
            await session.flush()  # Get the ID
            return str(db_request.id)

    async def store_analysis_result(self, result_data: AnalysisResultCreate) -> str:
        """Store analysis result and return ID"""
        async with self.get_session() as session:
            db_result = AnalysisResult(
                request_id=result_data.request_id,
                fraud_probability=result_data.fraud_probability,
                confidence=result_data.confidence,
                threat_level=result_data.threat_level,
                risk_indicators=result_data.risk_indicators,
                processing_time_ms=result_data.processing_time_ms,
                analyzer_version=result_data.analyzer_version,
                rules_triggered=result_data.rules_triggered
            )
            session.add(db_result)
            await session.flush()
            return str(db_result.id)

    async def store_user_feedback(self, feedback_data: UserFeedbackCreate) -> str:
        """Store user feedback and return ID"""
        async with self.get_session() as session:
            db_feedback = UserFeedback(
                result_id=feedback_data.result_id,
                is_correct=feedback_data.is_correct,
                user_comment=feedback_data.user_comment
            )
            session.add(db_feedback)
            await session.flush()
            return str(db_feedback.id)

    async def get_user_by_api_key(self, api_key: str) -> Optional[User]:
        """Get user by API key"""
        async with self.get_session() as session:
            result = await session.execute(
                text("SELECT * FROM users WHERE api_key = :api_key AND is_active = true"),
                {"api_key": api_key}
            )
            row = result.fetchone()
            if row:
                return User(**dict(row._mapping))
            return None

    async def create_user(self, api_key: str, name: str = None, email: str = None) -> str:
        """Create a new user and return ID"""
        async with self.get_session() as session:
            db_user = User(
                api_key=api_key,
                name=name,
                email=email
            )
            session.add(db_user)
            await session.flush()
            return str(db_user.id)
    
    async def store_analysis(self, analysis: AnalysisResult) -> str:
        """Store analysis result"""
        try:
            # Generate ID if not provided
            if not analysis.id:
                analysis.id = str(uuid.uuid4())
            
            # Convert to dict and add timestamp
            data = analysis.dict()
            data['created_at'] = datetime.utcnow().isoformat()
            
            # Append to file
            filepath = os.path.join(self.data_dir, "analyses.jsonl")
            async with aiofiles.open(filepath, 'a') as f:
                await f.write(json.dumps(data) + '\n')
            
            logger.info(f"💾 Stored analysis: {analysis.id}")
            return analysis.id
            
        except Exception as e:
            logger.error(f"❌ Failed to store analysis: {e}")
            raise
    
    async def store_feedback(self, feedback: UserFeedback) -> str:
        """Store user feedback"""
        try:
            # Generate ID if not provided
            if not feedback.id:
                feedback.id = str(uuid.uuid4())
            
            # Convert to dict and add timestamp
            data = feedback.dict()
            data['created_at'] = datetime.utcnow().isoformat()
            
            # Append to file
            filepath = os.path.join(self.data_dir, "feedback.jsonl")
            async with aiofiles.open(filepath, 'a') as f:
                await f.write(json.dumps(data) + '\n')
            
            logger.info(f"💾 Stored feedback: {feedback.id}")
            return feedback.id
            
        except Exception as e:
            logger.error(f"❌ Failed to store feedback: {e}")
            raise
    
    async def get_recent_analyses(self, limit: int = 100) -> List[Dict]:
        """Get recent analysis results"""
        try:
            filepath = os.path.join(self.data_dir, "analyses.jsonl")
            if not os.path.exists(filepath):
                return []
            
            analyses = []
            async with aiofiles.open(filepath, 'r') as f:
                lines = await f.readlines()
                
            # Get last N lines
            for line in lines[-limit:]:
                if line.strip():
                    analyses.append(json.loads(line.strip()))
            
            return analyses
            
        except Exception as e:
            logger.error(f"❌ Failed to get recent analyses: {e}")
            return []
    
    async def get_feedback_for_analysis(self, analysis_id: str) -> List[Dict]:
        """Get feedback for specific analysis"""
        try:
            filepath = os.path.join(self.data_dir, "feedback.jsonl")
            if not os.path.exists(filepath):
                return []
            
            feedback_list = []
            async with aiofiles.open(filepath, 'r') as f:
                lines = await f.readlines()
                
            for line in lines:
                if line.strip():
                    feedback = json.loads(line.strip())
                    if feedback.get('analysis_id') == analysis_id:
                        feedback_list.append(feedback)
            
            return feedback_list
            
        except Exception as e:
            logger.error(f"❌ Failed to get feedback: {e}")
            return []
    
    async def store_performance_metrics(self, performance: ModelPerformance) -> str:
        """Store model performance metrics"""
        try:
            # Generate ID if not provided
            if not performance.id:
                performance.id = str(uuid.uuid4())
            
            # Convert to dict and add timestamp
            data = performance.dict()
            data['evaluation_date'] = datetime.utcnow().isoformat()
            
            # Append to file
            filepath = os.path.join(self.data_dir, "performance.jsonl")
            async with aiofiles.open(filepath, 'a') as f:
                await f.write(json.dumps(data) + '\n')
            
            logger.info(f"💾 Stored performance metrics: {performance.id}")
            return performance.id
            
        except Exception as e:
            logger.error(f"❌ Failed to store performance metrics: {e}")
            raise
    
    async def close(self):
        """Close database connections"""
        logger.info("💾 Database manager closed")
