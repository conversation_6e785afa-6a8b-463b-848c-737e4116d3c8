"""
Real AI Sentiment Analysis Model
Uses transformer models for sophisticated sentiment analysis
"""

import logging
import time
from typing import Dict, Optional
from transformers import pipeline, Pipeline
import torch

logger = logging.getLogger(__name__)


class SentimentResult:
    """Result of sentiment analysis"""
    def __init__(self, sentiment_score: float, sentiment_label: str, confidence: float):
        self.sentiment_score = sentiment_score
        self.sentiment_label = sentiment_label
        self.confidence = confidence


class SentimentAnalyzer:
    """Advanced sentiment analysis using transformer models"""
    
    def __init__(self):
        self.model_version = "2.0.0-transformer"
        self.is_loaded = False
        self.sentiment_pipeline: Optional[Pipeline] = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        logger.info(f"💭 SentimentAnalyzer initialized on {self.device}")
    
    async def load_model(self):
        """Load sentiment analysis model"""
        try:
            start_time = time.time()
            logger.info("💭 Loading sentiment analysis model...")
            
            # Load pre-trained sentiment analysis model
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                device=0 if torch.cuda.is_available() else -1
            )
            
            self.is_loaded = True
            load_time = (time.time() - start_time) * 1000
            logger.info(f"✅ Sentiment analysis model loaded in {load_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"❌ Failed to load sentiment analysis model: {e}")
            raise
    
    async def analyze_sentiment(self, text: str) -> SentimentResult:
        """Analyze sentiment of text"""
        if not self.is_loaded or not self.sentiment_pipeline:
            raise RuntimeError("Sentiment model not loaded")
        
        try:
            # Run sentiment analysis
            result = self.sentiment_pipeline(text)
            
            if isinstance(result, list) and len(result) > 0:
                prediction = result[0]
                label = prediction.get('label', 'NEUTRAL').upper()
                score = prediction.get('score', 0.5)
                
                # Convert to normalized sentiment score (-1 to 1)
                if 'NEGATIVE' in label:
                    sentiment_score = -score
                elif 'POSITIVE' in label:
                    sentiment_score = score
                else:
                    sentiment_score = 0.0
                
                return SentimentResult(
                    sentiment_score=sentiment_score,
                    sentiment_label=label,
                    confidence=score
                )
            
            # Fallback
            return SentimentResult(
                sentiment_score=0.0,
                sentiment_label='NEUTRAL',
                confidence=0.5
            )
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return SentimentResult(
                sentiment_score=0.0,
                sentiment_label='NEUTRAL',
                confidence=0.3
            )
