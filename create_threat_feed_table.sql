-- Create the threat_feed table
CREATE TABLE IF NOT EXISTS threat_feed (
    id SERIAL PRIMARY KEY,
    content_text TEXT NOT NULL,
    source_platform VARCHAR(50) NOT NULL,
    source_url TEXT,
    sentinel_score NUMERIC(5,2) NOT NULL,
    risk_factors TEXT[] NOT NULL DEFAULT '{}', -- Array of text
    confidence NUMERIC(5,2) NOT NULL,
    fraud_risk NUMERIC(5,2) NOT NULL,
    sentiment VARCHAR(20) NOT NULL,
    metadata JSONB, -- Flexible JSON storage for additional data
    content_id VARCHAR(255) UNIQUE NOT NULL, -- Unique identifier for the content
    ai_enhanced BOOLEAN NOT NULL DEFAULT FALSE,
    detected_patterns TEXT[] NOT NULL DEFAULT '{}', -- Array of text for AI-detected patterns
    emotional_intensity NUMERIC(5,2),
    analysis_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Optional: Add indexes for frequently queried columns to improve performance
CREATE INDEX IF NOT EXISTS idx_threat_feed_sentinel_score ON threat_feed (sentinel_score);
CREATE INDEX IF NOT EXISTS idx_threat_feed_source_platform ON threat_feed (source_platform);
CREATE INDEX IF NOT EXISTS idx_threat_feed_analysis_timestamp ON threat_feed (analysis_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_threat_feed_content_id ON threat_feed (content_id);
