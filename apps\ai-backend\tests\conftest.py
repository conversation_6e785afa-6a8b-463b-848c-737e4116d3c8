"""
Test configuration and fixtures for Sentinel Backend tests
"""

import pytest
import asyncio
from typing import As<PERSON><PERSON>enerator
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import St<PERSON>Pool

from main import app
from database.models import Base
from database.database import <PERSON>Manager
from config import Settings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_settings():
    """Test settings with overrides"""
    return Settings(
        database_url="sqlite+aiosqlite:///:memory:",
        redis_url="redis://localhost:6379/1",  # Use different DB for tests
        api_secret_key="test-secret-key",
        cors_origins=["http://localhost:3000"],
        debug=True,
        require_api_key=False,  # Disable for easier testing
        rate_limit_per_minute=1000,  # Higher limits for tests
        rate_limit_per_hour=10000
    )


@pytest.fixture
async def test_db():
    """Create test database"""
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async_session_maker = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    yield async_session_maker
    
    await engine.dispose()


@pytest.fixture
async def db_manager(test_db):
    """Database manager for tests"""
    db_manager = DatabaseManager("sqlite+aiosqlite:///:memory:")
    db_manager.async_session_maker = test_db
    db_manager.is_initialized = True
    return db_manager


@pytest.fixture
def client():
    """Test client for FastAPI app"""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
def sample_text_requests():
    """Sample text analysis requests for testing"""
    return [
        {
            "text": "Guaranteed 500% returns in 24 hours! Limited time offer!",
            "url": "https://twitter.com/scammer/status/123",
            "context": {"platform": "twitter"}
        },
        {
            "text": "This is a normal investment opportunity with reasonable returns.",
            "url": "https://linkedin.com/posts/legitimate-advisor",
            "context": {"platform": "linkedin"}
        },
        {
            "text": "URGENT! Secret insider trading information! Act now!",
            "context": {"platform": "telegram"}
        },
        {
            "text": "Hello, this is just a regular message with no fraud indicators.",
            "url": "https://reddit.com/r/investing/comments/123"
        }
    ]


@pytest.fixture
def sample_api_key():
    """Sample API key for testing"""
    return "test-api-key-12345678901234567890"


@pytest.fixture
def auth_headers(sample_api_key):
    """Authorization headers for testing"""
    return {"Authorization": f"Bearer {sample_api_key}"}


@pytest.fixture
async def test_user(db_manager, sample_api_key):
    """Create test user"""
    user_id = await db_manager.create_user(
        api_key=sample_api_key,
        name="Test User",
        email="<EMAIL>"
    )
    return user_id
