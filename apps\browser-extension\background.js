/**
 * Sentinel Browser Extension - Simple Background Service Worker
 */

console.log('🛡️ Sentinel Background Service starting...');

// Handle extension installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('🛡️ Sentinel Extension installed');
});

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('📨 Background received message:', message);

  switch (message.action) {
    case 'updateIcon':
      updateExtensionIcon(message.status);
      break;
    case 'showNotification':
      showNotification(message.title, message.message);
      break;
  }
});

function updateExtensionIcon(status) {
  console.log('🔄 Updating icon status:', status);
  // Simple icon update without complex logic
}

function showNotification(title, message) {
  console.log('🔔 Showing notification:', title, message);
  try {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/sentinel-32.png',
      title: title,
      message: message
    });
  } catch (error) {
    console.error('Notification failed:', error);
  }
}

console.log('🛡️ Sentinel Background Service ready');
