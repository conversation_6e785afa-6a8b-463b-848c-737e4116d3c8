#!/usr/bin/env python3
"""Quick test of AI components"""

import asyncio
from core.simple_ai_engine import SimpleF<PERSON><PERSON>Dete<PERSON>, SimpleSentimentAnalyzer, SimpleVideoAnalyzer

async def main():
    print("🧪 Testing AI Components")
    
    # Test fraud detection
    detector = SimpleFraudDetector()
    result = await detector.analyze_fraud_risk("GUARANTEED 500% returns in 24 hours!")
    print(f"✅ Fraud: {result['fraud_probability']:.2f} | Patterns: {result['detected_patterns']}")
    
    # Test sentiment
    sentiment = SimpleSentimentAnalyzer()
    result = await sentiment.analyze_sentiment("Amazing investment opportunity!")
    print(f"✅ Sentiment: {result['sentiment']} | Confidence: {result['confidence']:.2f}")
    
    # Test video
    video = SimpleVideoAnalyzer()
    result = await video.analyze_video_url("https://deepfake-generator.com/fake.mp4")
    print(f"✅ Video: {result['deepfake_probability']:.2f} | Level: {result['threat_level']}")
    
    print("🎉 All AI components working!")

if __name__ == "__main__":
    asyncio.run(main())
