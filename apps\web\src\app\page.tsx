'use client';

import { Badge } from '@/components/ui/badge';
import { ThreatFeedItem } from '@sentinel/shared-types';
import useSWR from 'swr';
import { useState, useEffect } from 'react';

// Demo notification system
const useNotifications = () => {
  const [notifications, setNotifications] = useState<Array<{id: string, message: string, type: 'success' | 'warning' | 'error'}>>([]);

  const addNotification = (message: string, type: 'success' | 'warning' | 'error' = 'success') => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { id, message, type }]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 5000);
  };

  return { notifications, addNotification };
};

const fetcher = (url: string) => fetch(url).then((res) => res.json());

export default function DashboardPage() {
  const [filter, setFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [sortBy, setSortBy] = useState<'timestamp' | 'score'>('timestamp');
  const [analysisContent, setAnalysisContent] = useState('');
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const { notifications, addNotification } = useNotifications();
  const { data, error, isLoading } = useSWR<{ status: string; data: ThreatFeedItem[] }>(
    'http://127.0.0.1:8000/api/feed',
    fetcher,
    {
      refreshInterval: 2000, // Faster refresh for demo - every 2 seconds
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
    }
  );

  // Add demo effect for new threats appearing
  useEffect(() => {
    if (data?.data && data.data.length > 0) {
      const highRiskCount = data.data.filter(item => item.analysis.sentinel_score < 40).length;
      if (highRiskCount > 0) {
        addNotification(`🚨 ${highRiskCount} critical threats detected!`, 'error');
      }
    }
  }, [data?.data?.length]);
  useEffect(() => {
    if (data?.data && data.data.length > 0) {
      const highRiskCount = data.data.filter(item => item.analysis.sentinel_score < 40).length;
      if (highRiskCount > 0) {
        addNotification(`🚨 ${highRiskCount} critical threats detected!`, 'error');
      }
    }
  }, [data?.data?.length]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="relative mb-8">
            <div className="w-20 h-20 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-3xl">🛡️</span>
            </div>
          </div>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold">Sentinel Enterprise</h2>
            <p className="text-lg text-muted-foreground">Initializing AI threat detection...</p>
            <div className="flex items-center justify-center gap-2 mt-4">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive">Failed to load threat feed</p>
      </div>
    );
  }

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 70) return 'default';
    if (score >= 40) return 'secondary';
    return 'destructive';
  };

  const getBadgeVariant = (score: number) => {
    if (score >= 70) return 'default';
    if (score >= 40) return 'secondary';
    return 'destructive';
  };

  const getThreatLevel = (score: number) => {
    if (score >= 70) return 'low';
    if (score >= 40) return 'medium';
    return 'high';
  };

  const analyzeContent = async () => {
    if (!analysisContent.trim()) return;

    setIsAnalyzing(true);
    setAnalysisResult(null);

    try {
      const response = await fetch('http://127.0.0.1:8000/api/analyze-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'dev-api-key-12345'
        },
        body: JSON.stringify({
          content_text: analysisContent,
          source_platform: 'Other',
          page_url: window.location.href
        }),
      });

      const result = await response.json();

      if (result.status === 'success') {
        setAnalysisResult(result.data);
      } else {
        throw new Error(result.message || 'Analysis failed');
      }

      // Add notification based on risk level
      if (result.data.sentinel_score < 30) {
        addNotification(`🚨 HIGH RISK THREAT DETECTED! Score: ${result.data.sentinel_score}`, 'error');
      } else if (result.data.sentinel_score < 60) {
        addNotification(`⚠️ Medium risk content identified. Score: ${result.data.sentinel_score}`, 'warning');
      } else {
        addNotification(`✅ Content appears safe. Score: ${result.data.sentinel_score}`, 'success');
      }
    } catch (error) {
      console.error('Analysis failed:', error);
      setAnalysisResult({ error: 'Analysis failed. Please try again.' });
      addNotification('❌ Analysis failed. Please try again.', 'error');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const filteredAndSortedData = data?.data
    ?.filter(item => {
      if (filter === 'all') return true;
      return getThreatLevel(item.sentinel_score) === filter;
    })
    ?.sort((a, b) => {
      if (sortBy === 'score') {
        return a.sentinel_score - b.sentinel_score;
      }
      return new Date(b.analysis_timestamp).getTime() - new Date(a.analysis_timestamp).getTime();
    }) || [];

  return (
    <div className="space-y-6">
      {/* Notifications */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`p-4 rounded-lg shadow-lg border-l-4 bg-white max-w-sm animate-in slide-in-from-right duration-300 ${
              notification.type === 'error' ? 'border-red-500' :
              notification.type === 'warning' ? 'border-yellow-500' : 'border-green-500'
            }`}
          >
            <p className={`text-sm font-medium ${
              notification.type === 'error' ? 'text-red-800' :
              notification.type === 'warning' ? 'text-yellow-800' : 'text-green-800'
            }`}>
              {notification.message}
            </p>
          </div>
        ))}
      </div>

      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
              🛡️ Sentinel Enterprise Intelligence
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-normal text-green-200">LIVE MONITORING</span>
              </div>
            </h1>
            <p className="text-blue-100 mt-2">
              Real-time AI-powered threat detection with deepfake analysis • {data?.data?.length || 0} threats monitored
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{data?.data?.filter(item => item.analysis.sentinel_score < 40).length || 0}</div>
            <div className="text-sm text-blue-200">Active Threats</div>
          </div>
        </div>

        {/* Real-time status indicators */}
        <div className="flex gap-6 mt-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-green-400 rounded-full"></div>
            <span>AI Engine: Online</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-green-400 rounded-full"></div>
            <span>Deepfake Detection: Active</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-green-400 rounded-full"></div>
            <span>Threat Intelligence: Synced</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-yellow-400 rounded-full"></div>
            <span>Queue: {isAnalyzing ? 'Processing' : 'Idle'}</span>
          </div>
        </div>
      </div>

      {/* Enhanced Real-time Stats Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-lg border border-red-200 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-red-500/10 rounded-full -mr-8 -mt-8"></div>
          <div className="relative">
            <div className="text-3xl font-bold text-red-600 flex items-center gap-2">
              {data?.data?.filter(item => item.analysis.sentinel_score < 40).length || 0}
              {(data?.data?.filter(item => item.analysis.sentinel_score < 40).length || 0) > 0 && (
                <span className="text-red-500 animate-pulse">🚨</span>
              )}
            </div>
            <div className="text-sm text-red-700 font-medium">Critical Threats</div>
            <div className="text-xs text-red-600 mt-1">Immediate attention required</div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-lg border border-orange-200 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-orange-500/10 rounded-full -mr-8 -mt-8"></div>
          <div className="relative">
            <div className="text-3xl font-bold text-orange-600">
              {data?.data?.filter(item => item.analysis.sentinel_score >= 40 && item.analysis.sentinel_score < 70).length || 0}
            </div>
            <div className="text-sm text-orange-700 font-medium">Medium Risk</div>
            <div className="text-xs text-orange-600 mt-1">Monitor closely</div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-lg border border-green-200 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-green-500/10 rounded-full -mr-8 -mt-8"></div>
          <div className="relative">
            <div className="text-3xl font-bold text-green-600">
              {data?.data?.filter(item => item.analysis.sentinel_score >= 70).length || 0}
            </div>
            <div className="text-sm text-green-700 font-medium">Low Risk</div>
            <div className="text-xs text-green-600 mt-1">Safe content</div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-full -mr-8 -mt-8"></div>
          <div className="relative">
            <div className="text-3xl font-bold text-blue-600">
              {data?.data?.length || 0}
            </div>
            <div className="text-sm text-blue-700 font-medium">Total Analyzed</div>
            <div className="text-xs text-blue-600 mt-1">Last 24 hours</div>
          </div>
        </div>
      </div>

      {/* Live Content Analysis Demo */}
      <div className="bg-card p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">🔍 Live Content Analysis</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Paste content to analyze for threats:
            </label>
            <textarea
              value={analysisContent}
              onChange={(e) => setAnalysisContent(e.target.value)}
              placeholder="Enter social media post, message, or any text content..."
              className="w-full h-32 p-3 border rounded-md resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          <div className="flex gap-3">
            <button
              onClick={analyzeContent}
              disabled={!analysisContent.trim() || isAnalyzing}
              className="px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  Analyzing...
                </>
              ) : (
                <>🛡️ Analyze Threat Level</>
              )}
            </button>
            <button
              onClick={() => {
                setAnalysisContent('');
                setAnalysisResult(null);
              }}
              className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90"
            >
              Clear
            </button>
          </div>

          {/* Demo Preset Content */}
          <div className="border-t pt-4">
            <p className="text-sm text-muted-foreground mb-2">Quick Demo Examples:</p>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setAnalysisContent("🚨 URGENT: Guaranteed 500% returns in 24 hours! Secret crypto algorithm revealed. Limited spots available!")}
                className="px-3 py-1 text-xs bg-red-100 text-red-800 rounded hover:bg-red-200"
              >
                High Risk Example
              </button>
              <button
                onClick={() => setAnalysisContent("Investment opportunity in emerging markets. Potential returns of 15-20% annually. Consult your advisor.")}
                className="px-3 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200"
              >
                Medium Risk Example
              </button>
              <button
                onClick={() => setAnalysisContent("Market analysis shows steady growth in tech sector. Diversified portfolio recommended for long-term investors.")}
                className="px-3 py-1 text-xs bg-green-100 text-green-800 rounded hover:bg-green-200"
              >
                Low Risk Example
              </button>
            </div>
          </div>

          {analysisResult && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              {analysisResult.error ? (
                <div className="text-destructive">❌ {analysisResult.error}</div>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <span className="text-lg font-semibold">Threat Assessment:</span>
                    <Badge variant={getBadgeVariant(analysisResult.sentinel_score)} className="text-sm">
                      Score: {analysisResult.sentinel_score}/100
                    </Badge>
                    <Badge variant="outline" className="text-sm">
                      {analysisResult.risk_level} RISK
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Confidence:</span> {Math.round(analysisResult.confidence * 100)}%
                    </div>
                    <div>
                      <span className="font-medium">Fraud Probability:</span> {Math.round(analysisResult.fraud_probability * 100)}%
                    </div>
                    <div>
                      <span className="font-medium">Processing Time:</span> {analysisResult.processing_time_ms}ms
                    </div>
                  </div>

                  {analysisResult.risk_factors && analysisResult.risk_factors.length > 0 && (
                    <div>
                      <span className="font-medium">Risk Factors:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {analysisResult.risk_factors.map((factor: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {factor.replace(/_/g, ' ').toUpperCase()}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 rounded text-sm ${filter === 'all' ? 'bg-primary text-primary-foreground' : 'bg-secondary'}`}
          >
            All Threats
          </button>
          <button
            onClick={() => setFilter('high')}
            className={`px-3 py-1 rounded text-sm ${filter === 'high' ? 'bg-destructive text-destructive-foreground' : 'bg-secondary'}`}
          >
            High Risk
          </button>
          <button
            onClick={() => setFilter('medium')}
            className={`px-3 py-1 rounded text-sm ${filter === 'medium' ? 'bg-orange-600 text-white' : 'bg-secondary'}`}
          >
            Medium Risk
          </button>
          <button
            onClick={() => setFilter('low')}
            className={`px-3 py-1 rounded text-sm ${filter === 'low' ? 'bg-green-600 text-white' : 'bg-secondary'}`}
          >
            Low Risk
          </button>
        </div>

        <div className="flex gap-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'timestamp' | 'score')}
            className="px-3 py-1 rounded border bg-background"
          >
            <option value="timestamp">Sort by Time</option>
            <option value="score">Sort by Risk Score</option>
          </select>
        </div>
      </div>

      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="h-12 px-4 text-left align-middle font-medium">
                  Content
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium">
                  Platform
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium">
                  Score
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium">
                  Risk Factors
                </th>
                <th className="h-12 px-4 text-left align-middle font-medium">
                  Timestamp
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredAndSortedData.map((item, index) => (
                <tr key={item.id} className={`border-b hover:bg-muted/50 transition-colors animate-in fade-in duration-500 ${
                  getThreatLevel(item.analysis.sentinel_score) === 'high' ? 'bg-red-50/50' : ''
                }`} style={{ animationDelay: `${index * 100}ms` }}>
                  <td className="p-4 align-middle">
                    <div className="max-w-md">
                      <div className="flex items-start gap-2">
                        {getThreatLevel(item.analysis.sentinel_score) === 'high' && (
                          <span className="text-red-500 text-lg animate-pulse">🚨</span>
                        )}
                        <div>
                          <div className="font-medium text-sm leading-relaxed">{item.content_text}</div>
                          {item.source_url && (
                            <a
                              href={item.source_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-xs text-blue-600 hover:underline flex items-center gap-1 mt-1"
                            >
                              🔗 View Source
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="p-4 align-middle">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="capitalize">
                        {item.source_platform === 'twitter' ? '🐦 Twitter' :
                         item.source_platform === 'telegram' ? '✈️ Telegram' :
                         item.source_platform === 'facebook' ? '📘 Facebook' :
                         item.source_platform === 'reddit' ? '🤖 Reddit' :
                         item.source_platform === 'discord' ? '💬 Discord' :
                         item.source_platform}
                      </Badge>
                    </div>
                  </td>
                  <td className="p-4 align-middle">
                    <div className="flex items-center gap-2">
                      <div className="flex flex-col items-center">
                        <Badge
                          variant={getBadgeVariant(item.analysis.sentinel_score)}
                          className="font-mono text-sm"
                        >
                          {item.analysis.sentinel_score}/100
                        </Badge>
                        <div className={`text-xs font-semibold mt-1 ${
                          getThreatLevel(item.analysis.sentinel_score) === 'high' ? 'text-red-600' :
                          getThreatLevel(item.analysis.sentinel_score) === 'medium' ? 'text-orange-600' : 'text-green-600'
                        }`}>
                          {getThreatLevel(item.analysis.sentinel_score).toUpperCase()}
                        </div>
                      </div>
                      {getThreatLevel(item.analysis.sentinel_score) === 'high' && (
                        <span className="text-red-500 animate-pulse">⚠️</span>
                      )}
                    </div>
                  </td>
                  <td className="p-4 align-middle">
                    <div className="space-y-1">
                      {item.analysis.risk_factors.slice(0, 2).map((factor) => (
                        <Badge key={factor} variant="outline" className={`mr-1 text-xs ${
                          factor.includes('deepfake') ? 'bg-red-100 text-red-800' :
                          factor.includes('fraud') || factor.includes('scam') ? 'bg-orange-100 text-orange-800' :
                          factor.includes('misinformation') ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {factor.replace(/_/g, ' ').toUpperCase()}
                        </Badge>
                      ))}
                      {item.analysis.risk_factors.length > 2 && (
                        <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800">
                          +{item.analysis.risk_factors.length - 2} more
                        </Badge>
                      )}
                      {item.analysis.risk_factors.length === 0 && (
                        <span className="text-xs text-muted-foreground">No risks detected</span>
                      )}
                    </div>
                  </td>
                  <td className="p-4 align-middle text-sm text-muted-foreground">
                    <div>{new Date(item.analysis_timestamp).toLocaleString()}</div>
                    <div className="text-xs">
                      Confidence: {Math.round(item.confidence * 100)}%
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {(!data?.data || data.data.length === 0) && !isLoading && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            No threats detected. System is monitoring...
          </p>
        </div>
      )}

      {filteredAndSortedData.length === 0 && data?.data && data.data.length > 0 && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">
            No threats match the current filter. Try adjusting your filters.
          </p>
        </div>
      )}

      {/* Real-time Status Indicator */}
      <div className="fixed bottom-4 right-4 bg-card border rounded-lg p-3 shadow-lg">
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${isLoading ? 'bg-blue-500 animate-pulse' : 'bg-green-500'}`}></div>
          <span className="text-sm text-muted-foreground">
            {isLoading ? 'Updating...' : 'Live monitoring active'}
          </span>
        </div>
      </div>
    </div>
  );
}