{"name": "project-sentinel-enterprise", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "test": "turbo test", "prepare": "husky install"}, "devDependencies": {"@turbo/gen": "^1.12.4", "eslint": "^8.57.0", "prettier": "^3.2.5", "husky": "^8.0.0", "lint-staged": "^15.2.2", "turbo": "^1.12.4"}, "packageManager": "npm@10.2.4", "workspaces": ["apps/*", "packages/*"]}