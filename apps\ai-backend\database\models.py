"""
Database models for Sentinel Rule-Based Backend
SQLAlchemy models for PostgreSQL database
"""

from datetime import datetime
from typing import Dict, Optional
import uuid

from sqlalchemy import Column, String, DateTime, Float, Boolean, Integer, Text, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, Field

Base = declarative_base()


class AnalysisRequest(Base):
    """Database model for analysis requests"""
    __tablename__ = "analysis_requests"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    source_type = Column(String(50), nullable=False)  # 'text', 'video', etc.
    content_hash = Column(String(64), nullable=False, index=True)  # SHA-256 hash of content
    url = Column(Text, nullable=True)
    platform = Column(String(50), nullable=True)  # 'twitter', 'reddit', etc.
    context_data = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationship to results
    results = relationship("AnalysisResult", back_populates="request")


class AnalysisResult(Base):
    """Database model for analysis results"""
    __tablename__ = "analysis_results"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    request_id = Column(UUID(as_uuid=True), ForeignKey("analysis_requests.id"), nullable=False)

    # Analysis results
    fraud_probability = Column(Float, nullable=False)
    confidence = Column(Float, nullable=False)
    threat_level = Column(String(20), nullable=False)  # 'critical', 'high', 'medium', 'low', 'minimal'
    risk_indicators = Column(JSON, nullable=True)  # List of risk indicators

    # Processing metadata
    processing_time_ms = Column(Float, nullable=False)
    analyzer_version = Column(String(20), nullable=False)
    rules_triggered = Column(JSON, nullable=True)  # Details of which rules were triggered

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    request = relationship("AnalysisRequest", back_populates="results")
    feedback = relationship("UserFeedback", back_populates="result")


class User(Base):
    """Database model for users (API key management)"""
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    api_key = Column(String(64), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=True)
    email = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    rate_limit_per_hour = Column(Integer, default=1000, nullable=False)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_used_at = Column(DateTime, nullable=True)


class UserFeedback(Base):
    """Database model for user feedback"""
    __tablename__ = "user_feedback"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    result_id = Column(UUID(as_uuid=True), ForeignKey("analysis_results.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    is_correct = Column(Boolean, nullable=False)
    user_comment = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    result = relationship("AnalysisResult", back_populates="feedback")
    user = relationship("User")


# Pydantic models for API serialization
class AnalysisRequestCreate(BaseModel):
    """Pydantic model for creating analysis requests"""
    source_type: str = Field(..., description="Type of content: text, video, etc.")
    content_hash: str = Field(..., description="SHA-256 hash of content")
    url: Optional[str] = Field(None, description="Source URL if available")
    platform: Optional[str] = Field(None, description="Platform: twitter, reddit, etc.")
    context_data: Optional[Dict] = Field(None, description="Additional context")


class AnalysisResultCreate(BaseModel):
    """Pydantic model for creating analysis results"""
    request_id: str = Field(..., description="ID of the analysis request")
    fraud_probability: float = Field(..., ge=0.0, le=1.0)
    confidence: float = Field(..., ge=0.0, le=1.0)
    threat_level: str = Field(..., description="Threat level")
    risk_indicators: list = Field(default_factory=list)
    processing_time_ms: float = Field(..., ge=0.0)
    analyzer_version: str = Field(..., description="Version of analyzer used")
    rules_triggered: Optional[Dict] = Field(None, description="Rules that were triggered")


class UserFeedbackCreate(BaseModel):
    """Pydantic model for creating user feedback"""
    result_id: str = Field(..., description="ID of the analysis result")
    is_correct: bool = Field(..., description="Whether the analysis was correct")
    user_comment: Optional[str] = Field(None, description="Optional user comment")
