#!/usr/bin/env python3
"""
Test the working AI system
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, '.')
sys.path.insert(0, '../..')

async def test_simple_ai():
    """Test the simplified AI engine."""
    print("🧪 Testing Simplified AI Engine")
    print("-" * 40)
    
    try:
        # Import the working AI components
        sys.path.append('../../core')
        from core.simple_ai_engine import SimpleFraudDetector, SimpleSentimentAnalyzer, SimpleVideoAnalyzer
        
        # Test fraud detection
        print("1. Testing Fraud Detection...")
        fraud_detector = SimpleFraudDetector()
        
        test_cases = [
            "🚨 GUARANTEED 500% returns in 24 hours! Secret algorithm!",
            "Market analysis shows steady growth in tech sector.",
            "URGENT! Don't miss out! Limited time crypto opportunity!"
        ]
        
        for i, test_text in enumerate(test_cases, 1):
            result = await fraud_detector.analyze_fraud_risk(test_text)
            print(f"   Test {i}: Fraud Probability = {result['fraud_probability']:.2f}")
            print(f"   Patterns: {result['detected_patterns']}")
            print()
        
        # Test sentiment analysis
        print("2. Testing Sentiment Analysis...")
        sentiment_analyzer = SimpleSentimentAnalyzer()
        
        for i, test_text in enumerate(test_cases, 1):
            result = await sentiment_analyzer.analyze_sentiment(test_text)
            print(f"   Test {i}: Sentiment = {result['sentiment']} (confidence: {result['confidence']:.2f})")
            print(f"   Manipulation: {result['manipulation_detected']}")
            print()
        
        # Test video analysis
        print("3. Testing Video Analysis...")
        video_analyzer = SimpleVideoAnalyzer()
        
        test_urls = [
            "https://deepfake-generator.com/fake-video.mp4",
            "https://youtube.com/watch?v=normal-video",
            "https://example.com/synthetic-face-swap.mp4"
        ]
        
        for i, test_url in enumerate(test_urls, 1):
            result = await video_analyzer.analyze_video_url(test_url)
            print(f"   Test {i}: Deepfake Probability = {result['deepfake_probability']:.2f}")
            print(f"   Threat Level: {result['threat_level']}")
            print()
        
        print("✅ ALL AI COMPONENTS WORKING!")
        return True
        
    except Exception as e:
        print(f"❌ AI Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_orchestrator():
    """Test the working orchestrator."""
    print("🎯 Testing AI Orchestrator")
    print("-" * 40)
    
    try:
        from core.simple_ai_engine import simple_ai_orchestrator
        
        test_content = "🚨 GUARANTEED 1000% profit! Secret insider algorithm! URGENT - limited spots!"
        content_id = "test_123"
        metadata = {"source": "test", "platform": "Twitter"}
        
        result = await simple_ai_orchestrator.analyze_content(test_content, content_id, metadata)
        
        print(f"✅ Sentinel Score: {result['sentinel_score']}/100")
        print(f"✅ Risk Level: {result['risk_level']}")
        print(f"✅ Fraud Probability: {result['fraud_analysis']['fraud_probability']:.2f}")
        print(f"✅ Sentiment: {result['sentiment_analysis']['sentiment']}")
        print(f"✅ Detected Patterns: {result['risk_factors']}")
        print(f"✅ Processing Time: {result['processing_time_ms']}ms")
        print(f"✅ AI Enhanced: {result['ai_analysis_complete']}")
        
        # Validate it's not random
        if result['fraud_analysis']['fraud_probability'] > 0.5:
            print("✅ AI correctly identified high fraud risk")
        else:
            print("⚠️ AI may need tuning")
        
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🛡️ SENTINEL AI VALIDATION")
    print("=" * 50)
    
    # Test individual components
    ai_test = await test_simple_ai()
    orchestrator_test = await test_orchestrator()
    
    print("=" * 50)
    print("🎯 TEST SUMMARY")
    print("=" * 50)
    
    if ai_test and orchestrator_test:
        print("🎉 ALL TESTS PASSED!")
        print("✅ System is ready for production")
        print("\nNext steps:")
        print("1. Start API server: python ../../scripts/start_dev.py")
        print("2. Test browser extension")
        print("3. Run demo script")
    else:
        print("❌ Some tests failed")
        print("🔧 Fix issues before deployment")

if __name__ == "__main__":
    asyncio.run(main())
