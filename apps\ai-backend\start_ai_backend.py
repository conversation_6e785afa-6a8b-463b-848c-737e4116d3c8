#!/usr/bin/env python3
"""
Sentinel AI Backend Startup Script
Starts the real AI backend with GenConViT deepfake detection
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'fastapi',
        'uvicorn', 
        'torch',
        'transformers',
        'timm',
        'opencv-python',
        'face-recognition'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"❌ {package} is missing")
    
    if missing_packages:
        logger.error(f"Missing packages: {', '.join(missing_packages)}")
        logger.info("Install with: pip install -r requirements.txt")
        return False
    
    return True

def download_genconvit_weights():
    """Download GenConViT pre-trained weights if not present"""
    weight_dir = Path("weight")
    weight_dir.mkdir(exist_ok=True)
    
    weights = [
        {
            "name": "genconvit_ed_inference.pth",
            "url": "https://huggingface.co/Deressa/GenConViT/resolve/main/genconvit_ed_inference.pth"
        },
        {
            "name": "genconvit_vae_inference.pth", 
            "url": "https://huggingface.co/Deressa/GenConViT/resolve/main/genconvit_vae_inference.pth"
        }
    ]
    
    for weight in weights:
        weight_path = weight_dir / weight["name"]
        if not weight_path.exists():
            logger.info(f"📥 Downloading {weight['name']}...")
            try:
                import requests
                response = requests.get(weight["url"], stream=True)
                response.raise_for_status()
                
                with open(weight_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.info(f"✅ Downloaded {weight['name']}")
            except Exception as e:
                logger.warning(f"⚠️ Could not download {weight['name']}: {e}")
                logger.info("The model will use random initialization for demo purposes")
        else:
            logger.info(f"✅ {weight['name']} already exists")

def create_data_directories():
    """Create necessary data directories"""
    directories = ["data", "weight", "logs"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"📁 Created directory: {directory}")

def start_backend():
    """Start the AI backend server"""
    logger.info("🚀 Starting Sentinel AI Backend with GenConViT...")
    
    try:
        # Start the FastAPI server
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "127.0.0.1",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
        
        logger.info(f"Running command: {' '.join(cmd)}")
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Server failed to start: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    logger.info("🛡️ Sentinel AI Backend Startup")
    logger.info("=" * 50)
    
    # Check dependencies
    logger.info("🔍 Checking dependencies...")
    if not check_dependencies():
        logger.error("❌ Dependencies check failed")
        sys.exit(1)
    
    # Create directories
    logger.info("📁 Creating directories...")
    create_data_directories()
    
    # Download weights
    logger.info("📥 Checking GenConViT weights...")
    download_genconvit_weights()
    
    # Start backend
    logger.info("🚀 Starting AI backend...")
    start_backend()

if __name__ == "__main__":
    main()
