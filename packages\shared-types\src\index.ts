// Shared types between frontend and backend for type safety across the platform

export interface IngestPayload {
  content_text: string;
  source_platform: 'Telegram' | 'Discord' | 'Reddit' | 'Twitter' | 'BSE' | 'NSE' | 'Other';
  source_url?: string;
  metadata?: Record<string, any>;
}

export interface SentinelAnalysis {
  sentinel_score: number;
  risk_factors: string[];
  confidence: number;
  fraud_risk: number;
  sentiment: 'positive' | 'negative' | 'neutral' | string; // Allow string for broader compatibility
  // analysis_timestamp: string; // Remove from here, move to top-level ThreatFeedItem
}

export interface ThreatFeedItem {
  id: number;
  content_text: string;
  source_platform: string;
  source_url?: string;
  // timestamp: string; // Remove this
  sentinel_score: number; // Add these fields directly
  risk_factors: string[];
  confidence: number;
  fraud_risk: number;
  sentiment: string; // Use string for sentiment
  analysis_timestamp: string; // Add this at top-level
  metadata?: Record<string, any>;
}

export interface HealthStatus {
  status: 'ok' | 'degraded' | 'down';
  dependencies: {
    database: boolean;
    queue: boolean;
  };
  timestamp: string;
}

export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
  error?: string;
}