# Sentinel Backend Configuration Template
# Copy this file to .env and fill in your values

# Application Settings
APP_NAME="Sentinel Rule-Based Backend"
APP_VERSION="1.0.0"
DEBUG=false
LOG_LEVEL=INFO

# Database Configuration
DATABASE_URL=postgresql://sentinel:sentinel_dev_password@localhost:5432/sentinel
DATABASE_ECHO=false

# Redis Configuration (for caching and rate limiting)
REDIS_URL=redis://localhost:6379/0

# Security Configuration
API_SECRET_KEY=your-super-secret-key-change-this-in-production
CORS_ORIGINS=http://localhost:3000,chrome-extension://
ALLOWED_HOSTS=localhost,127.0.0.1
REQUIRE_API_KEY=true
DEFAULT_API_KEY=

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Content Analysis Limits
MAX_TEXT_LENGTH=10000
MAX_URL_LENGTH=2048

# SSRF Prevention
ALLOWED_DOMAINS=twitter.com,reddit.com,youtube.com,linkedin.com,facebook.com
BLOCKED_IP_RANGES=*********/8,10.0.0.0/8,**********/12,***********/16

# Production Settings (uncomment for production)
# DEBUG=false
# LOG_LEVEL=WARNING
# REQUIRE_API_KEY=true
# CORS_ORIGINS=https://yourdomain.com
# DATABASE_URL=***************************************/sentinel
# REDIS_URL=redis://prod-redis:6379/0
