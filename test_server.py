#!/usr/bin/env python3
"""
Simple test server to verify the Sentinel API is working
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Sentinel Test Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Sentinel Enterprise API is running!", "status": "ok"}

@app.get("/health")
async def health():
    return {
        "status": "ok",
        "service": "sentinel-api",
        "version": "1.0.0",
        "timestamp": "2024-01-01T00:00:00Z"
    }

@app.get("/api/test")
async def test_endpoint():
    return {
        "status": "success",
        "data": {
            "message": "Test endpoint working",
            "features": [
                "Mock AI Analysis",
                "Mock Deepfake Detection", 
                "Mock Threat Intelligence",
                "Real-time Dashboard"
            ]
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
