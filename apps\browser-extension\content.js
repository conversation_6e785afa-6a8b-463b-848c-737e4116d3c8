/**
 * Sentinel Browser Extension - Content Script
 * Injects real-time threat intelligence into web pages
 */

class SentinelThreatOverlay {
  constructor() {
    this.apiBaseUrl = this.getApiBaseUrl();
    this.isAnalyzing = false;
    this.processedElements = new Set();
    this.videoObserver = null;
    this.threatCount = 0;
    this.apiKey = 'dev-api-key-12345';

    this.init();
  }

  getApiBaseUrl() {
    // Environment-aware API URL configuration
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      return 'http://127.0.0.1:8000';
    } else {
      // Production Vercel URL - will be configured via extension settings
      return 'https://your-sentinel-api.vercel.app';
    }
  }

  async init() {
    console.log('🛡️ Sentinel Threat Detection activated on:', window.location.href);

    // Initialize threat overlay system
    this.injectStyles();

    // Wait for page to load then start monitoring
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.startContentMonitoring();
        this.initializeVideoMonitoring();
      });
    } else {
      this.startContentMonitoring();
      this.initializeVideoMonitoring();
    }

    // Monitor for dynamic content
    this.observePageChanges();

    // Test API connection
    this.testApiConnection();
  }

  injectStyles() {
    if (document.getElementById('sentinel-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'sentinel-styles';
    style.textContent = `
      .sentinel-score-badge {
        display: inline-flex;
        align-items: center;
        padding: 2px 6px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        margin-left: 8px;
        cursor: pointer;
        z-index: 10000;
        position: relative;
      }
      
      .sentinel-score-high {
        background: linear-gradient(135deg, #059669, #10b981);
        color: white;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
      }
      .sentinel-score-medium {
        background: linear-gradient(135deg, #ea580c, #f59e0b);
        color: white;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
      }
      .sentinel-score-low {
        background: linear-gradient(135deg, #dc2626, #ef4444);
        color: white;
        animation: sentinelCriticalPulse 2s infinite;
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
      }
      
      .sentinel-tooltip {
        position: absolute;
        background: #1f2937;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        max-width: 300px;
        z-index: 10001;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        display: none;
      }
      
      .sentinel-analyzing {
        animation: pulse 1.5s infinite;
        background: #6366f1 !important;
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      @keyframes sentinelCriticalPulse {
        0%, 100% {
          transform: scale(1);
          box-shadow: 0 2px 8px rgba(220, 38, 38, 0.4);
        }
        50% {
          transform: scale(1.05);
          box-shadow: 0 4px 16px rgba(220, 38, 38, 0.7);
        }
      }
      
      .sentinel-video-warning {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        padding: 16px;
        border-radius: 8px;
        font-weight: bold;
        z-index: 10002;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        max-width: 350px;
      }
    `;
    document.head.appendChild(style);
  }

  startContentMonitoring() {
    // Monitor different platforms with more aggressive detection
    console.log('🛡️ Starting content monitoring on:', window.location.hostname);

    if (window.location.hostname.includes('twitter.com') || window.location.hostname.includes('x.com')) {
      this.monitorTwitter();
    } else if (window.location.hostname.includes('telegram.org')) {
      this.monitorTelegram();
    } else if (window.location.hostname.includes('reddit.com')) {
      this.monitorReddit();
    } else {
      // Generic monitoring for any site
      this.monitorGeneric();
    }

    // Also monitor all text content as fallback
    this.monitorAllText();
  }

  monitorTwitter() {
    const tweets = document.querySelectorAll('[data-testid="tweet"]');
    tweets.forEach(tweet => this.analyzeTweetContent(tweet));
  }

  monitorTelegram() {
    const messages = document.querySelectorAll('.message');
    messages.forEach(message => this.analyzeTelegramMessage(message));
  }

  monitorReddit() {
    const posts = document.querySelectorAll('[data-testid="post-container"]');
    posts.forEach(post => this.analyzeRedditPost(post));
  }

  async analyzeTweetContent(tweetElement) {
    if (this.processedElements.has(tweetElement)) return;
    this.processedElements.add(tweetElement);

    const textElement = tweetElement.querySelector('[data-testid="tweetText"]');
    if (!textElement) return;

    const content = textElement.textContent;
    if (!content || content.length < 10) return;

    await this.injectThreatScore(textElement, content, 'Twitter');
  }

  async analyzeTelegramMessage(messageElement) {
    if (this.processedElements.has(messageElement)) return;
    this.processedElements.add(messageElement);

    const textElement = messageElement.querySelector('.message-content');
    if (!textElement) return;

    const content = textElement.textContent;
    if (!content || content.length < 10) return;

    await this.injectThreatScore(textElement, content, 'Telegram');
  }

  async analyzeRedditPost(postElement) {
    if (this.processedElements.has(postElement)) return;
    this.processedElements.add(postElement);

    const titleElement = postElement.querySelector('h3');
    if (!titleElement) return;

    const content = titleElement.textContent;
    if (!content || content.length < 10) return;

    await this.injectThreatScore(titleElement, content, 'Reddit');
  }

  async injectThreatScore(targetElement, content, platform) {
    // Create analyzing badge first
    const analyzingBadge = this.createScoreBadge('...', 'analyzing');
    targetElement.appendChild(analyzingBadge);

    try {
      // Call Sentinel API for analysis
      const analysis = await this.getSentinelScore(content, platform);
      
      // Replace analyzing badge with actual score
      analyzingBadge.remove();
      const scoreBadge = this.createScoreBadge(analysis.sentinel_score, this.getScoreClass(analysis.sentinel_score));
      const tooltip = this.createTooltip(analysis);
      
      targetElement.appendChild(scoreBadge);
      targetElement.appendChild(tooltip);
      
      // Add hover events
      scoreBadge.addEventListener('mouseenter', () => {
        tooltip.style.display = 'block';
      });
      
      scoreBadge.addEventListener('mouseleave', () => {
        tooltip.style.display = 'none';
      });
      
    } catch (error) {
      analyzingBadge.remove();
      console.error('Sentinel analysis failed:', error);
    }
  }

  createScoreBadge(score, className) {
    const badge = document.createElement('span');
    badge.className = `sentinel-score-badge sentinel-score-${className}`;

    // Enhanced badge design for demo
    if (className === 'analyzing') {
      badge.innerHTML = `
        <span style="display: inline-flex; align-items: center; gap: 4px;">
          🛡️ <span class="sentinel-spinner"></span> Analyzing...
        </span>
      `;
    } else {
      const riskLevel = score < 30 ? 'CRITICAL' : score < 60 ? 'HIGH' : 'SAFE';
      const emoji = score < 30 ? '🚨' : score < 60 ? '⚠️' : '✅';

      badge.innerHTML = `
        <span style="display: inline-flex; align-items: center; gap: 4px;">
          ${emoji} ${riskLevel} (${score})
        </span>
      `;
    }

    return badge;
  }

  createTooltip(analysis) {
    const tooltip = document.createElement('div');
    tooltip.className = 'sentinel-tooltip';
    
    let riskFactorsHtml = '';
    if (analysis.risk_factors && analysis.risk_factors.length > 0) {
      riskFactorsHtml = `
        <div style="margin-top: 8px;">
          <strong>Risk Factors:</strong><br>
          ${analysis.risk_factors.map(factor => `• ${factor}`).join('<br>')}
        </div>
      `;
    }
    
    tooltip.innerHTML = `
      <div>
        <strong>Sentinel Score: ${analysis.sentinel_score}/100</strong><br>
        <small>Confidence: ${Math.round(analysis.confidence * 100)}%</small>
        ${riskFactorsHtml}
      </div>
    `;
    
    return tooltip;
  }

  getScoreClass(score) {
    if (score >= 70) return 'high';
    if (score >= 40) return 'medium';
    return 'low';
  }

  async getSentinelScore(content, platform) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: content
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const result = await response.json();

      // Handle the API response structure
      if (result.status === 'success' && result.data) {
        return result.data;
      } else {
        throw new Error('Invalid API response format');
      }

    } catch (error) {
      console.error('Sentinel API call failed:', error);

      // Return fallback analysis
      return {
        sentinel_score: this._getFallbackScore(content),
        risk_level: 'medium',
        risk_factors: ['API_UNAVAILABLE'],
        confidence: 0.3,
        fraud_probability: 0.5,
        sentiment: 'neutral'
      };
    }
  }

  _getFallbackScore(content) {
    // Simple fallback scoring based on keywords
    const text = content.toLowerCase();
    let score = 70; // Start with neutral score

    // High-risk keywords
    const highRiskWords = ['guaranteed', '100%', 'risk-free', 'secret', 'urgent', 'limited time'];
    const foundRiskWords = highRiskWords.filter(word => text.includes(word));
    score -= foundRiskWords.length * 15;

    // Positive indicators
    const positiveWords = ['analysis', 'research', 'diversified', 'long-term'];
    const foundPositiveWords = positiveWords.filter(word => text.includes(word));
    score += foundPositiveWords.length * 5;

    return Math.max(0, Math.min(100, score));
  }

  initializeVideoMonitoring() {
    // Monitor for video elements
    this.videoObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const videos = node.querySelectorAll ? node.querySelectorAll('video') : [];
            videos.forEach(video => this.monitorVideo(video));
            
            if (node.tagName === 'VIDEO') {
              this.monitorVideo(node);
            }
          }
        });
      });
    });

    this.videoObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Monitor existing videos
    document.querySelectorAll('video').forEach(video => this.monitorVideo(video));
  }

  async monitorVideo(videoElement) {
    if (videoElement.dataset.sentinelMonitored) return;
    videoElement.dataset.sentinelMonitored = 'true';

    videoElement.addEventListener('play', async () => {
      await this.analyzeVideoForDeepfake(videoElement);
    });
  }

  async analyzeVideoForDeepfake(videoElement) {
    if (this.isAnalyzing) return;
    this.isAnalyzing = true;

    try {
      // Show analyzing indicator
      this.showVideoAnalysisIndicator();

      // Get video URL or source
      const videoUrl = videoElement.src || videoElement.currentSrc || window.location.href;
      
      // Call deepfake detection API
      const response = await fetch(`${this.apiBaseUrl}/api/analyze-video`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          video_url: videoUrl,
          page_url: window.location.href
        })
      });

      if (response.ok) {
        const result = await response.json();

        // Handle API response structure
        const analysis = result.status === 'success' ? result.data : result;

        if (analysis.requires_warning || analysis.deepfake_probability > 0.6) {
          this.showDeepfakeWarning(analysis);
        } else if (analysis.deepfake_probability > 0.3) {
          this.showDeepfakeAlert(analysis);
        }
      } else {
        console.error('Video analysis API failed:', response.status);
      }

    } catch (error) {
      console.error('Video analysis failed:', error);
    } finally {
      this.isAnalyzing = false;
      this.hideVideoAnalysisIndicator();
    }
  }

  showVideoAnalysisIndicator() {
    // Update extension icon to show analysis in progress
    chrome.runtime.sendMessage({
      action: 'updateIcon',
      status: 'analyzing'
    });
  }

  hideVideoAnalysisIndicator() {
    chrome.runtime.sendMessage({
      action: 'updateIcon', 
      status: 'idle'
    });
  }

  showDeepfakeWarning(analysis) {
    // Create critical warning notification
    const warning = document.createElement('div');
    warning.className = 'sentinel-video-warning';
    warning.innerHTML = `
      <div style="display: flex; align-items: center; margin-bottom: 8px;">
        <span style="font-size: 20px; margin-right: 8px;">🚨</span>
        <strong>SENTINEL ALERT: DEEPFAKE DETECTED</strong>
      </div>
      <div style="font-size: 13px; opacity: 0.9;">
        Probability: ${Math.round(analysis.deepfake_probability * 100)}% |
        Confidence: ${Math.round(analysis.confidence * 100)}%
      </div>
      <div style="font-size: 11px; margin-top: 4px; opacity: 0.8;">
        Model: ${analysis.model_version || 'Unknown'}
      </div>
      <button onclick="this.parentElement.remove()" style="
        position: absolute; top: 8px; right: 8px;
        background: none; border: none; color: white;
        font-size: 16px; cursor: pointer;
      ">×</button>
    `;

    document.body.appendChild(warning);

    // Auto-remove after 15 seconds for critical warnings
    setTimeout(() => {
      if (warning.parentElement) {
        warning.remove();
      }
    }, 15000);

    // Send browser notification
    chrome.runtime.sendMessage({
      action: 'showNotification',
      title: 'CRITICAL: Deepfake Detected!',
      message: `High probability deepfake detected (${Math.round(analysis.deepfake_probability * 100)}% confidence)`
    });
  }

  showDeepfakeAlert(analysis) {
    // Create moderate alert for medium-risk content
    const alert = document.createElement('div');
    alert.className = 'sentinel-video-warning';
    alert.style.background = '#f59e0b'; // Orange for medium risk
    alert.innerHTML = `
      <div style="display: flex; align-items: center; margin-bottom: 8px;">
        <span style="font-size: 18px; margin-right: 8px;">⚠️</span>
        <strong>SENTINEL: Potential Synthetic Content</strong>
      </div>
      <div style="font-size: 13px; opacity: 0.9;">
        Probability: ${Math.round(analysis.deepfake_probability * 100)}% |
        Confidence: ${Math.round(analysis.confidence * 100)}%
      </div>
      <button onclick="this.parentElement.remove()" style="
        position: absolute; top: 8px; right: 8px;
        background: none; border: none; color: white;
        font-size: 16px; cursor: pointer;
      ">×</button>
    `;

    document.body.appendChild(alert);

    // Auto-remove after 8 seconds
    setTimeout(() => {
      if (alert.parentElement) {
        alert.remove();
      }
    }, 8000);
  }

  monitorGeneric() {
    // Generic monitoring for any website
    const textElements = document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6');
    textElements.forEach(element => {
      if (element.textContent && element.textContent.length > 20) {
        this.analyzeGenericContent(element);
      }
    });
  }

  monitorAllText() {
    // Aggressive text monitoring as fallback
    const allText = document.body.innerText;
    if (allText && allText.length > 50) {
      // Look for high-risk phrases
      const highRiskPhrases = [
        'guaranteed profit', 'risk-free investment', 'secret algorithm',
        'urgent opportunity', 'limited time', '500% returns', '1000% profit'
      ];

      const foundPhrases = highRiskPhrases.filter(phrase =>
        allText.toLowerCase().includes(phrase)
      );

      if (foundPhrases.length > 0) {
        console.log('🚨 High-risk content detected:', foundPhrases);
        this.showPageWarning(foundPhrases);
      }
    }
  }

  async analyzeGenericContent(element) {
    if (this.processedElements.has(element)) return;
    this.processedElements.add(element);

    const content = element.textContent.trim();
    if (!content || content.length < 20) return;

    // Only analyze if it looks like financial content
    const financialKeywords = ['investment', 'profit', 'returns', 'trading', 'crypto', 'stock', 'money'];
    const hasFinancialContent = financialKeywords.some(keyword =>
      content.toLowerCase().includes(keyword)
    );

    if (hasFinancialContent) {
      await this.injectThreatScore(element, content, 'Generic');
    }
  }

  showPageWarning(riskPhrases) {
    // Show page-level warning for high-risk content
    const warning = document.createElement('div');
    warning.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #ef4444;
      color: white;
      padding: 12px;
      border-radius: 8px;
      font-weight: bold;
      z-index: 10000;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    warning.innerHTML = `
      <div style="display: flex; align-items: center; margin-bottom: 8px;">
        🛡️ <strong>SENTINEL ALERT</strong>
      </div>
      <div style="font-size: 12px;">
        High-risk content detected: ${riskPhrases.join(', ')}
      </div>
      <button onclick="this.parentElement.remove()" style="
        position: absolute; top: 4px; right: 8px;
        background: none; border: none; color: white;
        cursor: pointer;
      ">×</button>
    `;

    document.body.appendChild(warning);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (warning.parentElement) {
        warning.remove();
      }
    }, 10000);
  }

  observePageChanges() {
    // Re-scan for new content when page changes
    const observer = new MutationObserver(() => {
      setTimeout(() => this.startContentMonitoring(), 1000);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
}

  async testApiConnection() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        console.log('✅ Sentinel API connection successful');
        this.showConnectionStatus('connected');
      } else {
        console.log('⚠️ Sentinel API connection failed:', response.status);
        this.showConnectionStatus('error');
      }
    } catch (error) {
      console.log('❌ Sentinel API unreachable:', error);
      this.showConnectionStatus('offline');
    }
  }

  showConnectionStatus(status) {
    // Show connection status in corner
    const statusDiv = document.createElement('div');
    statusDiv.id = 'sentinel-status';
    statusDiv.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      font-weight: bold;
      z-index: 10000;
      ${status === 'connected' ? 'background: #10b981; color: white;' :
        status === 'error' ? 'background: #f59e0b; color: white;' :
        'background: #ef4444; color: white;'}
    `;

    const statusText = {
      'connected': '🛡️ Sentinel Active',
      'error': '⚠️ Sentinel Limited',
      'offline': '❌ Sentinel Offline'
    };

    statusDiv.textContent = statusText[status];

    // Remove existing status
    const existing = document.getElementById('sentinel-status');
    if (existing) existing.remove();

    document.body.appendChild(statusDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (statusDiv.parentElement) {
        statusDiv.remove();
      }
    }, 5000);
  }
}

// Initialize Sentinel when page loads
console.log('🛡️ Sentinel extension loading...');

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    console.log('🛡️ DOM loaded, starting Sentinel...');
    new SentinelThreatOverlay();
  });
} else {
  console.log('🛡️ DOM ready, starting Sentinel...');
  new SentinelThreatOverlay();
}
