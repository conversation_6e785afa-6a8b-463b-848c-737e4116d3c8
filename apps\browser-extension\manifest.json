{
  "manifest_version": 3,
  "name": "Sentinel - AI Threat Detection",
  "version": "1.0.0",
  "description": "Real-time financial fraud and deepfake detection for retail investors",
  "permissions": [
    "activeTab",
    "storage",
    "notifications",
    "scripting"
  ],
  "host_permissions": [
    "https://twitter.com/*",
    "https://x.com/*", 
    "https://web.telegram.org/*",
    "https://youtube.com/*",
    "https://www.youtube.com/*",
    "https://reddit.com/*",
    "https://www.reddit.com/*",
    "https://*.bse.com/*",
    "https://*.nse.com/*",
    "http://127.0.0.1:8000/*" // Added for local API access
  ],
  "background": {
    "service_worker": "background.js"
  },
  "content_scripts": [
    {
      "matches": [
        "https://twitter.com/*",
        "https://x.com/*",
        "https://web.telegram.org/*",
        "https://youtube.com/*",
        "https://www.youtube.com/*",
        "https://reddit.com/*",
        "https://www.reddit.com/*",
        "file:///*"
      ],
      "js": ["clean_content.js"],
      "css": ["sentinel-overlay.css"],
      "run_at": "document_end"
    }
  ],
  "action": {
    "default_popup": "popup.html",
    "default_title": "Sentinel Threat Detection"
  },
  "icons": {
    "16": "icons/sentinel-16.png",
    "32": "icons/sentinel-32.png", 
    "48": "icons/sentinel-48.png",
    "128": "icons/sentinel-128.png"
  },
  "web_accessible_resources": [
    {
      "resources": ["sentinel-overlay.css", "icons/*"],
      "matches": ["<all_urls>"]
    }
  ]
}