/**
 * SIMPLE WORKING SENTINEL EXTENSION
 * Guaranteed to work for demo
 */

console.log('🛡️ SENTINEL EXTENSION LOADED!');

class SimpleSentinel {
  constructor() {
    this.apiUrl = 'http://127.0.0.1:8000';
    this.mediaRecorder = null;
    this.recordedChunks = [];
    this.analysisHistory = [];
    this.isRecording = false;
    this.stats = {
      threatsBlocked: 0,
      contentAnalyzed: 0
    };
    this.init();
  }

  async init() {
    console.log('🛡️ Sentinel initializing...');

    // Add styles
    this.addStyles();

    // Load existing stats
    await this.loadStats();

    // Test API connection
    await this.testAPI();

    // Start monitoring
    this.startMonitoring();

    // Initialize video analysis
    this.initVideoAnalysis();

    // Show activation message
    this.showActivationMessage();

    // Force immediate scan after 2 seconds
    setTimeout(() => {
      console.log('🔍 Forcing immediate content scan...');
      this.scanAllText();
    }, 2000);
  }

  addStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .sentinel-badge {
        display: inline-block;
        padding: 3px 8px;
        margin: 0 4px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: bold;
        color: white;
        vertical-align: middle;
        white-space: nowrap;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      .sentinel-badge:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      }
      .sentinel-critical { background: #ef4444; animation: pulse 2s infinite; }
      .sentinel-high { background: #f59e0b; }
      .sentinel-safe { background: #10b981; }
      .sentinel-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #1f2937;
        color: white;
        padding: 16px;
        border-radius: 8px;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      }
      .sentinel-video-button {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #ef4444;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: bold;
        cursor: pointer;
        z-index: 10001;
        transition: all 0.2s ease;
      }
      .sentinel-video-button:hover {
        background: #dc2626;
        transform: scale(1.05);
      }
      .sentinel-video-button.recording {
        background: #10b981;
        animation: pulse 1s infinite;
      }
      .sentinel-analysis-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        z-index: 10002;
        max-width: 500px;
        width: 90%;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    `;
    document.head.appendChild(style);
  }

  async loadStats() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['threatsBlocked', 'contentAnalyzed']);
        this.stats.threatsBlocked = result.threatsBlocked || 0;
        this.stats.contentAnalyzed = result.contentAnalyzed || 0;
        console.log('📊 Stats loaded:', this.stats);
      }
    } catch (error) {
      console.log('📊 Stats loading failed:', error);
    }
  }

  async updateStats(contentAnalyzed = 0, threatsBlocked = 0) {
    this.stats.contentAnalyzed += contentAnalyzed;
    this.stats.threatsBlocked += threatsBlocked;

    console.log('📊 Stats updated:', this.stats);

    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({
          threatsBlocked: this.stats.threatsBlocked,
          contentAnalyzed: this.stats.contentAnalyzed
        });
      }
    } catch (error) {
      console.log('📊 Stats update failed:', error);
    }
  }

  async testAPI() {
    try {
      const response = await fetch(`${this.apiUrl}/api/health`);
      if (response.ok) {
        console.log('✅ Sentinel API connected');
        return true;
      }
    } catch (error) {
      console.log('❌ Sentinel API failed:', error);
    }
    return false;
  }

  startMonitoring() {
    // Monitor all text content
    this.scanAllText();
    
    // Monitor for new content
    const observer = new MutationObserver(() => {
      setTimeout(() => this.scanAllText(), 1000);
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  scanAllText() {
    // Find all text elements, being more inclusive
    const elements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, div, span');
    console.log(`🔍 Scanning ${elements.length} elements for threats...`);

    let processed = 0;
    let threatsFound = 0;

    elements.forEach(element => {
      if (element.dataset.sentinelProcessed) return;

      const text = element.textContent?.trim();
      if (!text || text.length < 15) return;

      // Skip if element is too nested or likely not main content
      if (this.isElementTooNested(element)) return;

      // Check for high-risk keywords
      const riskScore = this.calculateRisk(text);
      if (riskScore > 10) { // Lower threshold to catch more content
        console.log(`🚨 Risk detected: ${riskScore} in "${text.substring(0, 50)}..."`);
        this.addBadge(element, riskScore);
        element.dataset.sentinelProcessed = 'true';
        processed++;

        // Update statistics
        const isHighRisk = riskScore > 40;
        if (isHighRisk) threatsFound++;
        this.updateStats(1, isHighRisk ? 1 : 0);
      }
    });

    console.log(`📊 Scan complete: ${processed} badges added, ${threatsFound} threats found`);
  }

  isElementTooNested(element) {
    // Skip elements that are too deeply nested (likely UI elements)
    let depth = 0;
    let current = element;
    while (current.parentElement && depth < 10) {
      current = current.parentElement;
      depth++;

      // Skip if inside navigation, header, footer, or sidebar
      const className = current.className?.toLowerCase() || '';
      const tagName = current.tagName?.toLowerCase() || '';

      if (className.includes('nav') || className.includes('header') ||
          className.includes('footer') || className.includes('sidebar') ||
          tagName === 'nav' || tagName === 'header' || tagName === 'footer') {
        return true;
      }
    }

    return depth > 8; // Too deeply nested
  }

  calculateRisk(text) {
    const lower = text.toLowerCase();
    let risk = 0;

    // Critical fraud indicators (higher scores)
    if (lower.includes('guaranteed') || lower.includes('100%') || lower.includes('risk-free')) {
      risk += 50;
    }
    if (lower.includes('urgent') || lower.includes('limited time') || lower.includes('act now')) {
      risk += 35;
    }
    if (lower.includes('secret') || lower.includes('insider') || lower.includes('exclusive')) {
      risk += 40;
    }
    if (lower.includes('500%') || lower.includes('1000%') || lower.includes('millionaire')) {
      risk += 45;
    }

    // Additional high-risk terms
    if (lower.includes('profit') && (lower.includes('24 hours') || lower.includes('overnight'))) {
      risk += 30;
    }
    if (lower.includes('pump') || lower.includes('moon') || lower.includes('rocket')) {
      risk += 25;
    }
    if (lower.includes('algorithm') && lower.includes('revealed')) {
      risk += 35;
    }
    if (lower.includes('fomo') || lower.includes('regret') || lower.includes('miss out')) {
      risk += 20;
    }

    // Emotional manipulation
    if (lower.includes('forever') && lower.includes('opportunity')) {
      risk += 15;
    }

    console.log(`🔍 Risk calculation for "${text.substring(0, 30)}...": ${risk}`);
    return Math.min(risk, 95);
  }

  addBadge(element, riskScore) {
    const score = Math.max(0, 100 - riskScore);
    const badge = document.createElement('span');

    let className, text;
    if (score < 30) {
      className = 'sentinel-critical';
      text = `🚨 CRITICAL (${score})`;
    } else if (score < 70) {
      className = 'sentinel-high';
      text = `⚠️ HIGH (${score})`;
    } else {
      className = 'sentinel-safe';
      text = `✅ SAFE (${score})`;
    }

    badge.className = `sentinel-badge ${className}`;
    badge.textContent = text;
    badge.title = `Sentinel Score: ${score}/100 - Click for details`;

    // Add click handler
    badge.addEventListener('click', () => {
      this.showAnalysisDetails(score, riskScore);
    });

    // Smart placement - try to place at the end of the first line
    this.placeBadgeSmartly(element, badge);
    console.log(`🛡️ Added Sentinel badge: ${score}/100 to element`);
  }

  placeBadgeSmartly(element, badge) {
    // Strategy 1: If it's a heading, place after the heading text
    if (element.tagName && element.tagName.match(/^H[1-6]$/)) {
      element.appendChild(badge);
      return;
    }

    // Strategy 2: If it's a paragraph, try to place at the end of first sentence
    if (element.tagName === 'P') {
      const text = element.textContent;
      const firstSentence = text.split('.')[0];
      if (firstSentence.length < text.length && firstSentence.length < 100) {
        // Create a wrapper for better positioning
        const wrapper = document.createElement('span');
        wrapper.style.display = 'inline';
        wrapper.appendChild(badge);
        element.appendChild(wrapper);
        return;
      }
    }

    // Strategy 3: For divs, try to find the best text node
    if (element.tagName === 'DIV') {
      const textNodes = this.getTextNodes(element);
      if (textNodes.length > 0) {
        const firstTextNode = textNodes[0];
        if (firstTextNode.parentNode) {
          firstTextNode.parentNode.appendChild(badge);
          return;
        }
      }
    }

    // Fallback: append to element
    element.appendChild(badge);
  }

  getTextNodes(element) {
    const textNodes = [];
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let node;
    while (node = walker.nextNode()) {
      if (node.textContent.trim().length > 10) {
        textNodes.push(node);
      }
    }
    return textNodes;
  }

  showAnalysisDetails(score, riskScore) {
    const notification = document.createElement('div');
    notification.className = 'sentinel-notification';
    notification.innerHTML = `
      <div style="display: flex; align-items: center; margin-bottom: 8px;">
        <span style="font-size: 20px; margin-right: 8px;">🛡️</span>
        <strong>Sentinel Analysis</strong>
      </div>
      <div>Score: ${score}/100</div>
      <div>Risk Level: ${score < 30 ? 'CRITICAL' : score < 70 ? 'HIGH' : 'SAFE'}</div>
      <div>Confidence: 85%</div>
      <button onclick="this.parentElement.remove()" style="
        position: absolute; top: 8px; right: 8px;
        background: none; border: none; color: white;
        cursor: pointer; font-size: 16px;
      ">×</button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }

  initVideoAnalysis() {
    // Monitor for video elements (YouTube, etc.)
    this.addVideoButtons();

    // Watch for new videos being loaded
    const observer = new MutationObserver(() => {
      setTimeout(() => this.addVideoButtons(), 1000);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  addVideoButtons() {
    // Find all video elements
    const videos = document.querySelectorAll('video');
    console.log(`🎬 Found ${videos.length} video elements`);

    videos.forEach((video, index) => {
      if (video.dataset.sentinelButton) return;
      video.dataset.sentinelButton = 'true';

      console.log(`🎬 Adding button to video ${index + 1}`);

      // Create deepfake analysis button
      const button = document.createElement('button');
      button.className = 'sentinel-video-button';
      button.innerHTML = '🛡️ Analyze';
      button.title = 'Sentinel: Analyze for deepfakes';

      // Make button more visible
      button.style.cssText = `
        position: absolute !important;
        top: 10px !important;
        right: 10px !important;
        background: #ef4444 !important;
        color: white !important;
        border: none !important;
        padding: 8px 12px !important;
        border-radius: 6px !important;
        font-size: 12px !important;
        font-weight: bold !important;
        cursor: pointer !important;
        z-index: 999999 !important;
        transition: all 0.2s ease !important;
      `;

      button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('🎬 Analyze button clicked');
        this.analyzeVideo(video, button);
      });

      // Find the best container for the button
      let container = video.parentElement;

      // For YouTube, look for specific containers
      if (window.location.hostname.includes('youtube.com')) {
        const ytContainer = video.closest('.html5-video-player') ||
                           video.closest('.video-stream') ||
                           video.parentElement;
        if (ytContainer) {
          container = ytContainer;
        }
      }

      if (container) {
        container.style.position = 'relative';
        container.appendChild(button);
        console.log('🎬 Button added to container');
      } else {
        // Fallback: add to body with fixed positioning
        button.style.position = 'fixed';
        button.style.top = '20px';
        button.style.right = '20px';
        document.body.appendChild(button);
        console.log('🎬 Button added to body (fallback)');
      }
    });
  }

  async analyzeVideo(videoElement, button) {
    if (this.isRecording) {
      console.log('🎬 Already recording, skipping...');
      return;
    }

    console.log('🎬 Starting video analysis...');
    button.innerHTML = '🔴 Recording...';
    button.style.background = '#10b981';
    this.isRecording = true;

    try {
      // Show immediate feedback
      this.showNotification('🎬 Recording video segment...', 'info');

      // Simulate recording process (simplified for demo)
      await this.simulateRecording(3000);

      button.innerHTML = '🤖 Analyzing...';
      button.style.background = '#3b82f6';

      // Perform analysis
      const analysis = await this.performSimplifiedAnalysis(videoElement);

      // Show results
      this.showAnalysisResults(analysis);

      // Update statistics
      const isHighRisk = analysis.result.deepfake_probability > 0.5;
      this.updateStats(1, isHighRisk ? 1 : 0);

      console.log('🎬 Video analysis completed successfully');

    } catch (error) {
      console.error('🎬 Video analysis failed:', error);
      this.showError('Analysis failed: ' + error.message);
    } finally {
      button.innerHTML = '🛡️ Analyze';
      button.style.background = '#ef4444';
      this.isRecording = false;
    }
  }

  async simulateRecording(duration) {
    return new Promise(resolve => {
      let elapsed = 0;
      const interval = setInterval(() => {
        elapsed += 500;
        console.log(`🎬 Recording... ${elapsed}ms / ${duration}ms`);

        if (elapsed >= duration) {
          clearInterval(interval);
          resolve();
        }
      }, 500);
    });
  }

  async performSimplifiedAnalysis(videoElement) {
    try {
      // Get video information
      const videoInfo = {
        src: videoElement.src || videoElement.currentSrc || window.location.href,
        duration: videoElement.duration || 0,
        currentTime: videoElement.currentTime || 0,
        videoWidth: videoElement.videoWidth || 0,
        videoHeight: videoElement.videoHeight || 0
      };

      console.log('🎬 Video info:', videoInfo);

      // Call API for analysis
      const response = await fetch(`${this.apiUrl}/api/analyze-video`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          video_url: videoInfo.src,
          page_url: window.location.href,
          video_metadata: videoInfo,
          timestamp: new Date().toISOString()
        })
      });

      let result;
      if (response.ok) {
        const apiResult = await response.json();
        result = apiResult.data || apiResult;
        console.log('🎬 API analysis result:', result);
      } else {
        console.log('🎬 API failed, using fallback analysis');
        result = this.getFallbackVideoAnalysis(videoInfo.src);
      }

      // Create analysis object
      const analysis = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        videoInfo: videoInfo,
        result: result,
        url: window.location.href
      };

      // Store in history
      this.analysisHistory.unshift(analysis);
      if (this.analysisHistory.length > 10) {
        this.analysisHistory = this.analysisHistory.slice(0, 10);
      }

      return analysis;

    } catch (error) {
      console.error('🎬 Analysis error:', error);

      // Return fallback analysis
      return {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        videoInfo: { src: window.location.href },
        result: this.getFallbackVideoAnalysis(window.location.href),
        url: window.location.href
      };
    }
  }

  getFallbackVideoAnalysis(videoUrl) {
    const url = (videoUrl || '').toLowerCase();
    let probability = 0.15; // Base low probability
    let indicators = [];

    // Check for suspicious patterns in URL
    if (url.includes('deepfake') || url.includes('fake')) {
      probability += 0.6;
      indicators.push('suspicious_url_pattern');
    }
    if (url.includes('synthetic') || url.includes('ai-generated')) {
      probability += 0.4;
      indicators.push('ai_content_indicators');
    }
    if (url.includes('manipulated') || url.includes('edited')) {
      probability += 0.3;
      indicators.push('manipulation_keywords');
    }

    // Random variation for demo purposes
    probability += (Math.random() - 0.5) * 0.3;
    probability = Math.max(0.05, Math.min(0.95, probability));

    const threatLevel = probability > 0.7 ? 'critical' :
                       probability > 0.4 ? 'high' :
                       probability > 0.2 ? 'medium' : 'low';

    return {
      deepfake_probability: probability,
      confidence: 0.75 + (Math.random() * 0.2),
      threat_level: threatLevel,
      analysis_method: 'pattern_analysis',
      requires_warning: probability > 0.6,
      risk_indicators: indicators,
      processing_time_ms: 1500 + Math.random() * 1000
    };
  }

  showActivationMessage() {
    const message = document.createElement('div');
    message.className = 'sentinel-notification';
    message.style.background = '#10b981';
    message.innerHTML = `
      <div style="display: flex; align-items: center;">
        <span style="font-size: 20px; margin-right: 8px;">🛡️</span>
        <strong>Sentinel Extension Active!</strong>
      </div>
      <div style="font-size: 12px; margin-top: 4px;">
        Real-time threat & deepfake detection enabled<br>
        Stats: ${this.stats.contentAnalyzed} analyzed, ${this.stats.threatsBlocked} blocked
      </div>
    `;

    document.body.appendChild(message);

    setTimeout(() => {
      if (message.parentElement) {
        message.remove();
      }
    }, 4000);
  }

  showAnalysisResults(analysis) {
    const popup = document.createElement('div');
    popup.className = 'sentinel-analysis-popup';

    const result = analysis.result;
    const probability = Math.round(result.deepfake_probability * 100);
    const confidence = Math.round(result.confidence * 100);

    let threatColor, threatIcon, threatText;
    if (probability > 70) {
      threatColor = '#ef4444';
      threatIcon = '🚨';
      threatText = 'CRITICAL DEEPFAKE DETECTED';
    } else if (probability > 40) {
      threatColor = '#f59e0b';
      threatIcon = '⚠️';
      threatText = 'POSSIBLE DEEPFAKE';
    } else {
      threatColor = '#10b981';
      threatIcon = '✅';
      threatText = 'LIKELY AUTHENTIC';
    }

    popup.innerHTML = `
      <div style="text-align: center; margin-bottom: 20px;">
        <div style="font-size: 48px; margin-bottom: 8px;">${threatIcon}</div>
        <h2 style="color: ${threatColor}; margin: 0; font-size: 18px;">${threatText}</h2>
      </div>

      <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 16px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span><strong>Deepfake Probability:</strong></span>
          <span style="color: ${threatColor}; font-weight: bold;">${probability}%</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span><strong>Confidence:</strong></span>
          <span>${confidence}%</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span><strong>Analysis Method:</strong></span>
          <span>${result.analysis_method || 'AI Detection'}</span>
        </div>
      </div>

      <div style="display: flex; gap: 8px; justify-content: center;">
        <button onclick="this.parentElement.parentElement.remove()"
                style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
          Close
        </button>
        <button onclick="window.sentinelShowHistory()"
                style="background: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
          View History
        </button>
      </div>
    `;

    document.body.appendChild(popup);

    // Auto-remove after 15 seconds
    setTimeout(() => {
      if (popup.parentElement) {
        popup.remove();
      }
    }, 15000);

    // Show browser notification for high-risk content
    if (probability > 70) {
      this.showBrowserNotification('CRITICAL: Deepfake Detected!',
        `${probability}% probability of synthetic content detected`);
    }
  }

  showBrowserNotification(title, message) {
    // Try to show browser notification
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification(title, {
          body: message,
          icon: '/icons/sentinel-32.png'
        });
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            new Notification(title, {
              body: message,
              icon: '/icons/sentinel-32.png'
            });
          }
        });
      }
    }
  }

  showError(message) {
    const notification = document.createElement('div');
    notification.className = 'sentinel-notification';
    notification.style.background = '#ef4444';
    notification.innerHTML = `
      <div style="display: flex; align-items: center;">
        <span style="font-size: 20px; margin-right: 8px;">❌</span>
        <strong>Sentinel Error</strong>
      </div>
      <div style="font-size: 12px; margin-top: 4px;">
        ${message}
      </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }

  showAnalysisHistory() {
    const popup = document.createElement('div');
    popup.className = 'sentinel-analysis-popup';
    popup.style.maxWidth = '600px';
    popup.style.maxHeight = '80vh';
    popup.style.overflow = 'auto';

    let historyHtml = `
      <div style="text-align: center; margin-bottom: 20px;">
        <h2 style="margin: 0; color: #1f2937;">🛡️ Sentinel Analysis History</h2>
      </div>
    `;

    if (this.analysisHistory.length === 0) {
      historyHtml += `
        <div style="text-align: center; padding: 40px; color: #6b7280;">
          <div style="font-size: 48px; margin-bottom: 16px;">📊</div>
          <p>No video analyses yet</p>
          <p style="font-size: 14px;">Click "🛡️ Analyze" on any video to start</p>
        </div>
      `;
    } else {
      this.analysisHistory.forEach((analysis, index) => {
        const result = analysis.result;
        const probability = Math.round(result.deepfake_probability * 100);
        const date = new Date(analysis.timestamp).toLocaleString();

        let threatColor, threatIcon;
        if (probability > 70) {
          threatColor = '#ef4444';
          threatIcon = '🚨';
        } else if (probability > 40) {
          threatColor = '#f59e0b';
          threatIcon = '⚠️';
        } else {
          threatColor = '#10b981';
          threatIcon = '✅';
        }

        historyHtml += `
          <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin-bottom: 12px; background: #f9fafb;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 20px;">${threatIcon}</span>
                <strong style="color: ${threatColor};">${probability}% Deepfake Probability</strong>
              </div>
              <small style="color: #6b7280;">${date}</small>
            </div>
            <div style="font-size: 12px; color: #6b7280; margin-bottom: 8px;">
              ${analysis.url}
            </div>
            <div style="display: flex; gap: 8px;">
              <button onclick="window.sentinelPlayVideo(${index})"
                      style="background: #3b82f6; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                Play Recording
              </button>
              <button onclick="window.sentinelDeleteAnalysis(${index})"
                      style="background: #ef4444; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                Delete
              </button>
            </div>
          </div>
        `;
      });
    }

    historyHtml += `
      <div style="text-align: center; margin-top: 20px;">
        <button onclick="this.parentElement.parentElement.remove()"
                style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
          Close
        </button>
      </div>
    `;

    popup.innerHTML = historyHtml;
    document.body.appendChild(popup);
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = 'sentinel-notification';

    const colors = {
      'info': '#3b82f6',
      'success': '#10b981',
      'warning': '#f59e0b',
      'error': '#ef4444'
    };

    notification.style.background = colors[type] || colors.info;
    notification.innerHTML = `
      <div style="display: flex; align-items: center;">
        <span style="font-size: 16px; margin-right: 8px;">🛡️</span>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 3000);
  }
}

// Global functions for popup interactions
window.sentinelShowHistory = function() {
  const sentinel = window.sentinelInstance;
  if (sentinel) {
    sentinel.showAnalysisHistory();
  } else {
    alert('Sentinel not initialized');
  }
};

window.sentinelPlayVideo = function(index) {
  const sentinel = window.sentinelInstance;
  if (sentinel && sentinel.analysisHistory[index]) {
    const analysis = sentinel.analysisHistory[index];
    const videoUrl = URL.createObjectURL(analysis.videoBlob);

    // Create video player popup
    const popup = document.createElement('div');
    popup.className = 'sentinel-analysis-popup';
    popup.innerHTML = `
      <div style="text-align: center; margin-bottom: 16px;">
        <h3 style="margin: 0;">🎬 Recorded Video Segment</h3>
      </div>
      <video controls style="width: 100%; max-width: 400px; border-radius: 8px;">
        <source src="${videoUrl}" type="video/webm">
        Your browser does not support the video tag.
      </video>
      <div style="text-align: center; margin-top: 16px;">
        <button onclick="this.parentElement.remove()"
                style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">
          Close
        </button>
      </div>
    `;

    document.body.appendChild(popup);
  }
};

window.sentinelDeleteAnalysis = function(index) {
  const sentinel = window.sentinelInstance;
  if (sentinel && sentinel.analysisHistory[index]) {
    if (confirm('Delete this analysis?')) {
      sentinel.analysisHistory.splice(index, 1);
      // Refresh history display
      document.querySelectorAll('.sentinel-analysis-popup').forEach(p => p.remove());
      sentinel.showAnalysisHistory();
    }
  }
};

// Add global method for testing
window.forceSentinelScan = function() {
  if (window.sentinelInstance) {
    console.log('🔍 Forcing Sentinel scan...');
    window.sentinelInstance.scanAllText();
  } else {
    console.log('❌ Sentinel not found');
  }
};

// Start immediately
console.log('🛡️ Starting Enhanced Sentinel...');
window.sentinelInstance = new SimpleSentinel();
